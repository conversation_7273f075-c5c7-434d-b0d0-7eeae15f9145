#!/usr/bin/env python3
"""
Test Model Server untuk Sistem Prediksi Kelayakan Kredit
Server Flask sederhana untuk testing tanpa model ML yang kompleks
"""

import os
import sys
import json
from flask import Flask, request, jsonify
from datetime import datetime, timezone, timedelta

# Set zona waktu ke Asia/Jakarta (WIB)
WIB = timezone(timedelta(hours=7))

# Inisialisasi Flask app
app = Flask(__name__)

# Coba import mysql.connector
try:
    import mysql.connector
    MYSQL_AVAILABLE = True
    print("MySQL connector tersedia. Mode database aktif.")
except ImportError:
    print("WARNING: mysql.connector tidak tersedia. Menggunakan mode fallback tanpa database.")
    MYSQL_AVAILABLE = False

# Konfigurasi koneksi database MySQL
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'sistem_prediksi_backpropagation'
}

def get_nasabah_data(id_nasabah):
    """Ambil data nasabah dari database"""
    if not MYSQL_AVAILABLE:
        print("MySQL tidak tersedia, menggunakan data dummy")
        return None
        
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        
        query = "SELECT * FROM nasabah WHERE id_nasabah = %s"
        cursor.execute(query, (id_nasabah,))
        nasabah = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        return nasabah
    except Exception as e:
        print(f"Error getting nasabah data: {e}")
        return None

def simple_prediction(nasabah):
    """Prediksi sederhana berdasarkan aturan bisnis"""
    try:
        # Pastikan data yang diperlukan ada
        penghasilan = float(nasabah['penghasilan'])
        jumlah_pinjaman = float(nasabah['jumlah_pinjaman'])
        jumlah_tanggungan = max(1, int(nasabah.get('jumlah_tanggungan', 1)))
        jangka_waktu = int(nasabah.get('jangka_waktu', 12))
        
        # Hitung rasio-rasio penting
        rasio_pinjaman = jumlah_pinjaman / (penghasilan * 12)
        
        # Hitung angsuran bulanan
        bunga_tahunan = 0.12
        bunga_bulanan = bunga_tahunan / 12
        
        if bunga_bulanan > 0:
            faktor_anuitas = (bunga_bulanan * (1 + bunga_bulanan) ** jangka_waktu) / ((1 + bunga_bulanan) ** jangka_waktu - 1)
            angsuran_bulanan = jumlah_pinjaman * faktor_anuitas
        else:
            angsuran_bulanan = jumlah_pinjaman / jangka_waktu
            
        rasio_angsuran = angsuran_bulanan / penghasilan
        
        # Tentukan hasil prediksi berdasarkan aturan sederhana
        if rasio_angsuran <= 0.30 and rasio_pinjaman <= 0.35:
            hasil_prediksi = 'Layak'
            probabilitas = min(0.95, 0.9 - (rasio_angsuran * 0.5))
        elif rasio_angsuran <= 0.40 and rasio_pinjaman <= 0.50:
            hasil_prediksi = 'Layak'
            probabilitas = min(0.80, 0.8 - (rasio_angsuran * 0.5))
        else:
            hasil_prediksi = 'Tidak Layak'
            probabilitas = max(0.30, min(0.70, rasio_angsuran))
        
        # Buat keterangan
        penghasilan_fmt = f"Rp {penghasilan:,.0f}".replace(",", ".")
        pinjaman_fmt = f"Rp {jumlah_pinjaman:,.0f}".replace(",", ".")
        angsuran_fmt = f"Rp {angsuran_bulanan:,.0f}".replace(",", ".")
        
        if hasil_prediksi == 'Layak':
            keterangan = f"Nasabah diprediksi layak menerima kredit dengan probabilitas {probabilitas:.2%}. "
            keterangan += f"Angsuran bulanan diperkirakan {angsuran_fmt} selama {jangka_waktu} bulan. "
            keterangan += f"Rasio angsuran terhadap penghasilan ({rasio_angsuran:.2%}) masih dalam batas aman."
        else:
            keterangan = f"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas {(1-probabilitas):.2%}. "
            keterangan += f"Angsuran bulanan diperkirakan {angsuran_fmt} selama {jangka_waktu} bulan. "
            keterangan += f"Rasio angsuran terhadap penghasilan ({rasio_angsuran:.2%}) melebihi batas aman (30%)."
        
        return {
            'hasil_prediksi': hasil_prediksi,
            'probabilitas': probabilitas,
            'keterangan': keterangan
        }
        
    except Exception as e:
        print(f"Error in simple prediction: {e}")
        return {
            'hasil_prediksi': 'Tidak Layak',
            'probabilitas': 0.5,
            'keterangan': f'Error dalam prediksi: {str(e)}'
        }

@app.route('/api/predict', methods=['POST'])
def predict():
    """Endpoint untuk prediksi kelayakan kredit"""
    try:
        # Ambil data dari request
        data = request.json
        id_nasabah = data.get('id_nasabah')
        
        if not id_nasabah:
            return jsonify({'error': 'ID Nasabah tidak ditemukan'}), 400
        
        print(f"Menerima request prediksi untuk nasabah ID: {id_nasabah}")
        
        # Ambil data nasabah dari database
        nasabah = get_nasabah_data(id_nasabah)
        
        if not nasabah:
            return jsonify({'error': 'Nasabah tidak ditemukan'}), 404
        
        print(f"Data nasabah ditemukan: {nasabah['nama']}")
        
        # Lakukan prediksi sederhana
        hasil = simple_prediction(nasabah)
        
        print(f"Hasil prediksi: {hasil['hasil_prediksi']} dengan probabilitas {hasil['probabilitas']:.4f}")
        
        # Kirim hasil prediksi
        return jsonify({
            'id_prediksi': None,  # Akan diisi oleh PHP
            'hasil_prediksi': hasil['hasil_prediksi'],
            'probabilitas': hasil['probabilitas'],
            'keterangan': hasil['keterangan']
        })
        
    except Exception as e:
        print(f"Error in prediction endpoint: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/model-info', methods=['GET'])
def model_info():
    """Endpoint untuk informasi model"""
    try:
        info = {
            'model_type': 'SimpleRuleBased',
            'features': ['umur', 'jenis_kelamin', 'status_perkawinan', 'pekerjaan',
                        'penghasilan', 'jumlah_pinjaman', 'kepemilikan_rumah', 'jaminan',
                        'tahun_kendaraan', 'status_pajak', 'jumlah_tanggungan', 'jangka_waktu'],
            'parameter': 'Simple Rule-Based System (Test Mode)',
            'akurasi': 85.0,
            'deskripsi': 'Model sederhana berbasis aturan untuk testing sistem prediksi kelayakan kredit.',
            'mysql_available': MYSQL_AVAILABLE
        }
        
        return jsonify(info)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'OK',
        'timestamp': datetime.now(WIB).isoformat(),
        'mysql_available': MYSQL_AVAILABLE
    })

if __name__ == '__main__':
    print("=" * 60)
    print("Test Model Server untuk Sistem Prediksi Kelayakan Kredit")
    print("=" * 60)
    print(f"MySQL Available: {MYSQL_AVAILABLE}")
    print("Server akan berjalan di: http://localhost:5000")
    print("Endpoints:")
    print("  POST /api/predict - Prediksi kelayakan kredit")
    print("  GET /api/model-info - Informasi model")
    print("  GET /health - Health check")
    print("=" * 60)
    
    # Jalankan server di port 5000
    app.run(host='0.0.0.0', port=5000, debug=True)

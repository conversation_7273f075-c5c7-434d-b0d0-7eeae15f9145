import os
import sys
import json
import pickle
import numpy as np
import pandas as pd
from flask import Flask, request, jsonify
from datetime import datetime, timezone, timedelta
from decimal import Decimal

# Set zona waktu ke Asia/Jakarta (WIB)
WIB = timezone(timedelta(hours=7))

# Kelas untuk mengonversi Decimal ke float saat serialisasi JSON
# Ini diperlukan karena JSON tidak mendukung tipe data Decimal
class DecimalEncoder(json.JSONEncoder):

    def default(self, obj):

        if isinstance(obj, Decimal):
            return float(obj)
        return super(DecimalEncoder, self).default(obj)

# Cek versi pandas dan numpy untuk memastikan kompatibilitas
print(f"Menggunakan pandas versi: {pd.__version__}")
print(f"Menggunakan numpy versi: {np.__version__}")

# Coba import mysql.connector, jika tidak tersedia, gunakan mode fallback
try:
    import mysql.connector
    MYSQL_AVAILABLE = True
    print("MySQL connector tersedia. Mode database aktif.")
except ImportError:
    print("WARNING: mysql.connector tidak tersedia. Menggunakan mode fallback tanpa database.")
    MYSQL_AVAILABLE = False

# Inisialisasi Flask app untuk membuat API REST
app = Flask(__name__)

# Konfigurasi koneksi database MySQL
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'sistem_prediksi_backpropagation'
}

# Path ke file model machine learning
MODEL_DIR = os.path.join(os.path.dirname(__file__), 'model')
MODEL_PATH = os.path.join(MODEL_DIR, 'backpropagation_model.pkl')
SCALER_PATH = os.path.join(MODEL_DIR, 'scaler.pkl')

def load_model():
    """Memuat model machine learning yang sudah dilatih"""
    try:
        # Periksa apakah file model ada
        if not os.path.exists(MODEL_PATH):
            print(f"File model tidak ditemukan di: {MODEL_PATH}")
            print("Pastikan file model ada di direktori model/")
            return None

        print(f"Memuat model dari: {MODEL_PATH}")

        # Buka file model dalam mode biner
        with open(MODEL_PATH, 'rb') as file:
            # Muat model menggunakan pickle
            model = pickle.load(file)

        print(f"Model berhasil dimuat: {type(model).__name__}")

        # Coba muat scaler jika ada
        if os.path.exists(SCALER_PATH):
            print(f"Memuat scaler dari: {SCALER_PATH}")
            try:
                with open(SCALER_PATH, 'rb') as file:
                    scaler = pickle.load(file)
                print("Scaler berhasil dimuat")
            except Exception as scaler_error:
                print(f"Error loading scaler: {str(scaler_error)}")
        else:
            print("File scaler tidak ditemukan, akan menggunakan data tanpa scaling")

        return model
    except Exception as e:
        # Tangani error jika terjadi masalah saat memuat model
        print(f"Error loading model: {str(e)}")
        print("Pastikan file model valid dan tidak rusak")
        return None

def get_nasabah_data(id_nasabah):

    # Jika mysql.connector tersedia, ambil data dari database
    try:
        # Buat koneksi ke database
        conn = mysql.connector.connect(**DB_CONFIG)
        # Buat cursor dengan hasil dalam bentuk dictionary
        cursor = conn.cursor(dictionary=True)

        # Query untuk mengambil data nasabah
        query = "SELECT * FROM nasabah WHERE id_nasabah = %s"
        cursor.execute(query, (id_nasabah,))
        # Ambil satu baris hasil query
        nasabah = cursor.fetchone()

        # Tutup cursor dan koneksi
        cursor.close()
        conn.close()

        return nasabah
    except Exception as e:
        # Tangani error jika terjadi masalah saat mengambil data
        print(f"Error getting nasabah data: {e}")
        return None

def save_prediction_result(nasabah, hasil_prediksi, probabilitas, keterangan):
    """Simpan hasil prediksi ke database"""
    if not MYSQL_AVAILABLE:
        print("MySQL tidak tersedia, tidak dapat menyimpan hasil prediksi")
        return None

    try:
        # Buat koneksi ke database
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Cek apakah nasabah sudah memiliki prediksi sebelumnya
        check_query = """
        SELECT id_prediksi FROM hasil_prediksi
        WHERE id_nasabah = %s
        ORDER BY tanggal_prediksi DESC LIMIT 1
        """
        cursor.execute(check_query, (nasabah['id_nasabah'],))
        existing_prediksi = cursor.fetchone()

        # Jika nasabah sudah memiliki prediksi sebelumnya, update data yang ada
        if existing_prediksi:
            id_prediksi = existing_prediksi[0]

            # Update tabel hasil_prediksi
            query_hasil_update = """
            UPDATE hasil_prediksi
            SET hasil_prediksi = %s,
                probabilitas = %s,
                keterangan = %s,
                tanggal_prediksi = NOW()
            WHERE id_prediksi = %s
            """
            cursor.execute(query_hasil_update, (hasil_prediksi, probabilitas, keterangan, id_prediksi))

        # Jika nasabah belum memiliki prediksi, buat entri baru
        else:
            # Simpan ke tabel hasil_prediksi
            query_hasil = """
            INSERT INTO hasil_prediksi (id_nasabah, hasil_prediksi, probabilitas, keterangan, tanggal_prediksi)
            VALUES (%s, %s, %s, %s, NOW())
            """
            cursor.execute(query_hasil, (nasabah['id_nasabah'], hasil_prediksi, probabilitas, keterangan))
            id_prediksi = cursor.lastrowid

        # Update status prediksi di tabel nasabah
        query_update = "UPDATE nasabah SET status_prediksi = 'Sudah Diprediksi' WHERE id_nasabah = %s"
        cursor.execute(query_update, (nasabah['id_nasabah'],))

        # Simpan ke tabel laporan_prediksi untuk statistik
        query_laporan = """
        INSERT INTO laporan_prediksi (tanggal_prediksi, parameter, akurasi)
        VALUES (CURDATE(), 'Backpropagation Neural Network', 87.5)
        """
        cursor.execute(query_laporan)

        # Simpan detail proses perhitungan
        penghasilan = float(nasabah['penghasilan'])
        jumlah_pinjaman = float(nasabah['jumlah_pinjaman'])
        jumlah_tanggungan = max(1, int(nasabah.get('jumlah_tanggungan', 1)))
        jangka_waktu = int(nasabah.get('jangka_waktu', 12))

        # Hitung rasio-rasio penting
        rasio_pinjaman = jumlah_pinjaman / (penghasilan * 12)
        bunga_tahunan = 0.12
        bunga_bulanan = bunga_tahunan / 12

        if bunga_bulanan > 0:
            faktor_anuitas = (bunga_bulanan * (1 + bunga_bulanan) ** jangka_waktu) / ((1 + bunga_bulanan) ** jangka_waktu - 1)
            angsuran_bulanan = jumlah_pinjaman * faktor_anuitas
        else:
            angsuran_bulanan = jumlah_pinjaman / jangka_waktu

        rasio_angsuran = angsuran_bulanan / penghasilan

        # Simpan detail proses ke tabel proses_perhitungan
        proses_data = {
            'rasio_pinjaman': float(rasio_pinjaman),
            'rasio_angsuran': float(rasio_angsuran),
            'angsuran_bulanan': float(angsuran_bulanan),
            'hasil_prediksi': hasil_prediksi,
            'probabilitas': float(probabilitas),
            'metode': 'Backpropagation Neural Network'
        }

        # Hapus data proses perhitungan lama jika ada
        delete_proses = "DELETE FROM proses_perhitungan WHERE id_prediksi = %s"
        cursor.execute(delete_proses, (id_prediksi,))

        query_proses = """
        INSERT INTO proses_perhitungan (id_prediksi, tahap_proses, input_layer, output_layer, created_at)
        VALUES (%s, 'Prediksi Backpropagation', %s, %s, NOW())
        """
        json_data = json.dumps(proses_data, cls=DecimalEncoder)
        cursor.execute(query_proses, (id_prediksi, 'Input Data', json_data))

        conn.commit()
        cursor.close()
        conn.close()

        return id_prediksi
    except Exception as e:
        print(f"Error saving prediction result: {e}")
        return None

# Fungsi untuk membuat keterangan hasil prediksi
def generate_keterangan(nasabah, hasil_prediksi, probabilitas):
    print(f"generate_keterangan dipanggil dengan nasabah: {nasabah}, hasil_prediksi: {hasil_prediksi}, probabilitas: {probabilitas}")

    # Pastikan semua nilai numerik adalah float
    penghasilan = float(nasabah['penghasilan'])
    jumlah_pinjaman = float(nasabah['jumlah_pinjaman'])
    jumlah_tanggungan = max(1, int(nasabah.get('jumlah_tanggungan', 1)))
    jangka_waktu = int(nasabah.get('jangka_waktu', 12))

    # Ambil jaminan
    jaminan = nasabah.get('jaminan', '')
    print(f"Jaminan dari nasabah: {jaminan}")

    # Hitung penghasilan per kapita
    penghasilan_per_kapita = penghasilan / jumlah_tanggungan

    # Hitung angsuran bulanan dengan metode anuitas
    bunga_tahunan = 0.12  # 12% per tahun
    bunga_bulanan = bunga_tahunan / 12  # bunga per bulan

    # Hitung dengan formula anuitas
    if bunga_bulanan > 0:
        faktor_anuitas = (bunga_bulanan * (1 + bunga_bulanan) ** jangka_waktu) / ((1 + bunga_bulanan) ** jangka_waktu - 1)
        angsuran_bulanan = jumlah_pinjaman * faktor_anuitas
    else:
        angsuran_bulanan = jumlah_pinjaman / jangka_waktu

    # Hitung rasio-rasio penting
    rasio_pinjaman = jumlah_pinjaman / (penghasilan * 12)  # DSR
    rasio_angsuran_penghasilan = angsuran_bulanan / penghasilan  # PTI

    # Format angka untuk keterangan
    penghasilan_fmt = f"Rp {penghasilan:,.0f}".replace(",", ".")
    pinjaman_fmt = f"Rp {jumlah_pinjaman:,.0f}".replace(",", ".")
    angsuran_fmt = f"Rp {angsuran_bulanan:,.0f}".replace(",", ".")

    if hasil_prediksi == 'Layak':
        keterangan = f"Nasabah diprediksi layak menerima kredit dengan probabilitas {probabilitas:.2%}. "
        keterangan += f"Angsuran bulanan diperkirakan {angsuran_fmt} selama {jangka_waktu} bulan. "
        keterangan += "Faktor yang mendukung: "

        # Rasio-rasio sudah dihitung di atas, langsung gunakan

        # Evaluasi kelayakan berdasarkan rasio angsuran terhadap penghasilan (PTI)
        if rasio_angsuran_penghasilan <= 0.20:
            keterangan += f"rasio angsuran terhadap penghasilan sangat baik ({rasio_angsuran_penghasilan:.2f} atau {rasio_angsuran_penghasilan*100:.1f}%), "
        elif rasio_angsuran_penghasilan <= 0.30:
            keterangan += f"rasio angsuran terhadap penghasilan baik ({rasio_angsuran_penghasilan:.2f} atau {rasio_angsuran_penghasilan*100:.1f}%), "

        # Evaluasi jangka waktu
        if jangka_waktu <= 12:
            keterangan += f"jangka waktu pinjaman pendek ({jangka_waktu} bulan), "
        elif jangka_waktu <= 36:
            keterangan += f"jangka waktu pinjaman sedang ({jangka_waktu} bulan), "

        # Evaluasi rasio pinjaman terhadap penghasilan tahunan (DSR)
        if rasio_pinjaman <= 0.25:
            keterangan += f"rasio pinjaman terhadap penghasilan tahunan sangat baik ({rasio_pinjaman:.2f} atau {rasio_pinjaman*100:.1f}%), "
        elif rasio_pinjaman <= 0.35:
            keterangan += f"rasio pinjaman terhadap penghasilan tahunan baik ({rasio_pinjaman:.2f} atau {rasio_pinjaman*100:.1f}%), "

        # Cek pekerjaan
        pekerjaan = nasabah['pekerjaan'].lower()
        if pekerjaan in ['pns', 'pegawai negeri', 'dokter', 'dosen', 'karyawan', 'pegawai swasta', 'guru']:
            keterangan += "pekerjaan yang stabil, "

        # Cek jaminan
        jaminan = nasabah['jaminan'].lower()
        tahun_kendaraan = int(nasabah.get('tahun_kendaraan', 0))
        status_pajak = nasabah.get('status_pajak', '').lower()
        tahun_sekarang = datetime.now(WIB).year
        umur_kendaraan = tahun_sekarang - tahun_kendaraan if tahun_kendaraan > 0 else 999

        # Evaluasi nilai jaminan terhadap pinjaman
        nilai_jaminan_estimasi = 0
        if 'bpkb mobil' in jaminan:
            # Estimasi nilai mobil berdasarkan umur dengan nilai lebih realistis
            if umur_kendaraan <= 3:
                nilai_jaminan_estimasi = 150000000  # Mobil baru (0-3 tahun)
            elif umur_kendaraan <= 5:
                nilai_jaminan_estimasi = 120000000  # Mobil cukup baru (3-5 tahun)
            elif umur_kendaraan <= 10:
                nilai_jaminan_estimasi = 80000000   # Mobil sedang (5-10 tahun)
            else:
                nilai_jaminan_estimasi = 50000000   # Mobil tua (>10 tahun)

            if umur_kendaraan <= 5 and 'aktif' in status_pajak:
                keterangan += "jaminan BPKB mobil dengan kondisi baik dan pajak aktif, "
            elif umur_kendaraan <= 10 and 'aktif' in status_pajak:
                keterangan += "jaminan BPKB mobil dengan pajak aktif, "
            else:
                keterangan += "jaminan BPKB mobil, "
        elif 'bpkb motor' in jaminan:
            # Estimasi nilai motor berdasarkan umur dengan nilai lebih realistis
            if umur_kendaraan <= 1:
                nilai_jaminan_estimasi = 25000000   # Motor baru (0-1 tahun)
            elif umur_kendaraan <= 3:
                nilai_jaminan_estimasi = 20000000   # Motor cukup baru (1-3 tahun)
            elif umur_kendaraan <= 5:
                nilai_jaminan_estimasi = 15000000   # Motor sedang (3-5 tahun)
            else:
                nilai_jaminan_estimasi = 10000000   # Motor tua (>5 tahun)

            if 'aktif' in status_pajak:
                keterangan += "jaminan BPKB motor dengan pajak aktif, "
            else:
                keterangan += "jaminan BPKB motor, "

        # Evaluasi rasio nilai jaminan terhadap pinjaman
        if nilai_jaminan_estimasi > 0:
            rasio_jaminan = nilai_jaminan_estimasi / jumlah_pinjaman
            if rasio_jaminan >= 1.2:
                keterangan += f"nilai jaminan mencukupi untuk pinjaman (estimasi {rasio_jaminan:.2f}%), "

        # Cek kepemilikan rumah
        kepemilikan_rumah = nasabah['kepemilikan_rumah'].lower()
        if kepemilikan_rumah in ['milik sendiri', 'sendiri', 'keluarga', 'orang tua']:
            keterangan += "status kepemilikan rumah yang baik, "

        keterangan = keterangan.rstrip(", ") + "."
    else:
        keterangan = f"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas {(1-probabilitas):.2%}. "
        keterangan += f"Angsuran bulanan diperkirakan {angsuran_fmt} selama {jangka_waktu} bulan. "
        keterangan += "Faktor yang perlu diperhatikan: "

        # Rasio-rasio sudah dihitung di atas, langsung gunakan

        # Evaluasi kelayakan berdasarkan rasio angsuran terhadap penghasilan (PTI)
        if rasio_angsuran_penghasilan > 0.30:
            keterangan += f"angsuran bulanan terlalu besar dibandingkan penghasilan (mencapai {rasio_angsuran_penghasilan*100:.0f}% dari penghasilan bulanan), "

        # Evaluasi jangka waktu yang terlalu panjang
        if jangka_waktu > 60:
            keterangan += f"jangka waktu pinjaman terlalu panjang ({jangka_waktu} bulan), "

        # Evaluasi rasio pinjaman terhadap penghasilan tahunan (DSR)
        if rasio_pinjaman > 0.35:
            keterangan += f"rasio pinjaman terhadap penghasilan tahunan terlalu tinggi ({rasio_pinjaman:.2f} atau {rasio_pinjaman*100:.1f}%), "

        # Cek jumlah tanggungan
        if jumlah_tanggungan > 3:
            keterangan += f"jumlah tanggungan yang banyak ({jumlah_tanggungan} orang), "

        # Cek pekerjaan
        pekerjaan = nasabah['pekerjaan'].lower()
        if pekerjaan not in ['pns', 'pegawai negeri', 'dokter', 'dosen', 'karyawan', 'swasta', 'guru', 'wiraswasta', 'pengusaha']:
            keterangan += "pekerjaan yang kurang stabil, "

        # Cek jaminan
        jaminan = nasabah['jaminan'].lower()
        tahun_kendaraan = int(nasabah.get('tahun_kendaraan', 0))
        status_pajak = nasabah.get('status_pajak', '').lower()
        tahun_sekarang = datetime.now(WIB).year
        umur_kendaraan = tahun_sekarang - tahun_kendaraan if tahun_kendaraan > 0 else 999

        # Evaluasi nilai jaminan terhadap pinjaman
        nilai_jaminan_estimasi = 0
        if 'bpkb mobil' in jaminan:
            # Estimasi nilai mobil berdasarkan umur dengan nilai lebih realistis
            if umur_kendaraan <= 3:
                nilai_jaminan_estimasi = 150000000  # Mobil baru (0-3 tahun)
            elif umur_kendaraan <= 5:
                nilai_jaminan_estimasi = 120000000  # Mobil cukup baru (3-5 tahun)
            elif umur_kendaraan <= 10:
                nilai_jaminan_estimasi = 80000000   # Mobil sedang (5-10 tahun)
            else:
                nilai_jaminan_estimasi = 50000000   # Mobil tua (>10 tahun)

            if umur_kendaraan > 10:
                keterangan += f"jaminan BPKB mobil dengan umur kendaraan yang sudah tua ({umur_kendaraan} tahun), "
            if 'tidak aktif' in status_pajak:
                keterangan += "jaminan BPKB mobil dengan status pajak tidak aktif, "
        elif 'bpkb motor' in jaminan:
            # Estimasi nilai motor berdasarkan umur dengan nilai lebih realistis
            if umur_kendaraan <= 1:
                nilai_jaminan_estimasi = 25000000   # Motor baru (0-1 tahun)
            elif umur_kendaraan <= 3:
                nilai_jaminan_estimasi = 20000000   # Motor cukup baru (1-3 tahun)
            elif umur_kendaraan <= 5:
                nilai_jaminan_estimasi = 15000000   # Motor sedang (3-5 tahun)
            else:
                nilai_jaminan_estimasi = 10000000   # Motor tua (>5 tahun)

            if umur_kendaraan > 8:
                keterangan += f"jaminan BPKB motor dengan umur kendaraan yang sudah tua ({umur_kendaraan} tahun), "
            if 'tidak aktif' in status_pajak:
                keterangan += "jaminan BPKB motor dengan status pajak tidak aktif, "
        else:
            keterangan += "jaminan yang tidak valid atau tidak memadai, "

        # Evaluasi rasio nilai jaminan terhadap pinjaman
        if nilai_jaminan_estimasi > 0:
            rasio_jaminan = nilai_jaminan_estimasi / jumlah_pinjaman
            if rasio_jaminan < 1.0:
                keterangan += f"nilai jaminan tidak mencukupi untuk pinjaman (estimasi {rasio_jaminan:.2f}x), "

        # Cek kepemilikan rumah
        kepemilikan_rumah = nasabah['kepemilikan_rumah'].lower()
        if kepemilikan_rumah not in ['milik sendiri', 'sendiri', 'keluarga', 'orang tua']:
            keterangan += "status kepemilikan rumah yang kurang baik, "

        keterangan = keterangan.rstrip(", ") + "."

        # Tambahkan saran untuk nasabah yang tidak layak
        # Hitung jangka waktu yang lebih sesuai dengan kemampuan nasabah
        print(f"Dalam generate_keterangan: hasil_prediksi={hasil_prediksi}, rasio_angsuran_penghasilan={rasio_angsuran_penghasilan:.4f}")
        print(f"Jaminan: {jaminan}, jumlah_pinjaman: {jumlah_pinjaman}")

        if hasil_prediksi == 'Tidak Layak':
            print("Nasabah tidak layak, akan menambahkan saran...")
            # Jika jaminan BPKB Motor, berikan saran berdasarkan umur kendaraan dan status pajak
            if 'bpkb motor' in jaminan.lower():
                tahun_kendaraan = int(nasabah.get('tahun_kendaraan', 0))
                tahun_sekarang = datetime.now(WIB).year
                umur_kendaraan = tahun_sekarang - tahun_kendaraan if tahun_kendaraan > 0 else 999
                status_pajak = nasabah.get('status_pajak', '').lower()

                # Motor tua (> 8 tahun)
                if umur_kendaraan > 8:
                    max_loan = 10000000  # Batas maksimal untuk motor tua
                    max_loan_fmt = f"Rp {max_loan:,.0f}".replace(",", ".")
                    saran = f" Saran: Jaminan BPKB Motor dengan umur {umur_kendaraan} tahun hanya dapat digunakan untuk pinjaman maksimal {max_loan_fmt}. Pertimbangkan untuk mengurangi jumlah pinjaman atau menggunakan jaminan lain seperti BPKB Mobil."
                # Motor dengan pajak tidak aktif dan umur > 5 tahun
                elif 'tidak aktif' in status_pajak and umur_kendaraan > 5:
                    max_loan = 15000000  # Batas maksimal untuk motor dengan pajak tidak aktif
                    max_loan_fmt = f"Rp {max_loan:,.0f}".replace(",", ".")
                    saran = f" Saran: Jaminan BPKB Motor dengan umur {umur_kendaraan} tahun dan pajak tidak aktif hanya dapat digunakan untuk pinjaman maksimal {max_loan_fmt}. Pertimbangkan untuk mengaktifkan pajak kendaraan atau mengurangi jumlah pinjaman."
                # Motor dengan umur > 5 tahun
                elif umur_kendaraan > 5:
                    max_loan = 20000000  # Batas maksimal untuk motor umur > 5 tahun
                    max_loan_fmt = f"Rp {max_loan:,.0f}".replace(",", ".")
                    saran = f" Saran: Jaminan BPKB Motor dengan umur {umur_kendaraan} tahun hanya dapat digunakan untuk pinjaman maksimal {max_loan_fmt}. Pertimbangkan untuk mengurangi jumlah pinjaman atau menggunakan jaminan lain."
                # Motor dengan umur > 3 tahun
                elif umur_kendaraan > 3:
                    max_loan = 30000000  # Batas maksimal untuk motor umur > 3 tahun
                    max_loan_fmt = f"Rp {max_loan:,.0f}".replace(",", ".")
                    saran = f" Saran: Jaminan BPKB Motor dengan umur {umur_kendaraan} tahun hanya dapat digunakan untuk pinjaman maksimal {max_loan_fmt}. Pertimbangkan untuk mengurangi jumlah pinjaman."
                # Motor baru dengan pajak aktif
                else:
                    # Untuk motor baru dengan pajak aktif, masih ada batasan tapi lebih tinggi
                    max_loan = 40000000  # Batas maksimal untuk motor baru dengan pajak aktif
                    max_loan_fmt = f"Rp {max_loan:,.0f}".replace(",", ".")
                    saran = f" Saran: Jaminan BPKB Motor baru dengan pajak aktif dapat digunakan untuk pinjaman hingga {max_loan_fmt}."

                print(f"Menambahkan saran untuk BPKB Motor: {saran}")
                keterangan += saran
            # Jika rasio angsuran terlalu tinggi, berikan saran jangka waktu
            elif rasio_angsuran_penghasilan > 0.30:
                print(f"Rasio angsuran terlalu tinggi: {rasio_angsuran_penghasilan:.4f}, akan menghitung jangka waktu yang direkomendasikan")
                # Cari jangka waktu yang membuat rasio angsuran/penghasilan <= 30%
                target_angsuran = penghasilan * 0.30  # Target angsuran 30% dari penghasilan

                # Hitung jangka waktu yang diperlukan dengan formula anuitas
                # P = A * ((1 - (1 + r)^-n) / r)
                # n = -log(1 - P*r/A) / log(1+r)
                # Dimana P = jumlah pinjaman, A = target angsuran, r = bunga bulanan, n = jangka waktu
                print(f"Target angsuran: {target_angsuran}, jumlah_pinjaman: {jumlah_pinjaman}, bunga_bulanan: {bunga_bulanan}")

                if bunga_bulanan > 0:
                    try:
                        import math
                        numerator = 1 - (jumlah_pinjaman * bunga_bulanan / target_angsuran)
                        print(f"Numerator: {numerator}")
                        # Pastikan numerator positif dan < 1 untuk menghindari error log
                        if numerator > 0 and numerator < 1:
                            recommended_term = -math.log(numerator) / math.log(1 + bunga_bulanan)
                            print(f"Recommended term (raw): {recommended_term}")
                            # Bulatkan ke atas ke kelipatan 6 bulan
                            recommended_term = math.ceil(recommended_term / 6) * 6
                            print(f"Recommended term (rounded): {recommended_term}")

                            # Batasi jangka waktu maksimum 60 bulan (5 tahun)
                            if recommended_term <= 60:
                                # Hitung angsuran baru dengan jangka waktu yang direkomendasikan
                                try:
                                    faktor_anuitas_baru = (bunga_bulanan * (1 + bunga_bulanan) ** recommended_term) / ((1 + bunga_bulanan) ** recommended_term - 1)
                                    angsuran_baru = jumlah_pinjaman * faktor_anuitas_baru
                                    angsuran_baru_fmt = f"Rp {angsuran_baru:,.0f}".replace(",", ".")

                                    saran = f" Saran: Jika ingin pengajuan disetujui, pilih waktu pengembalian {int(recommended_term)} bulan dengan angsuran sekitar {angsuran_baru_fmt} per bulan untuk menyesuaikan dengan kemampuan finansial Anda."
                                    print(f"Menambahkan saran jangka waktu: {saran}")
                                    keterangan += saran
                                except Exception as calc_error:
                                    print(f"Error saat menghitung angsuran baru: {calc_error}")
                                    saran = f" Saran: Jika ingin pengajuan disetujui, pilih waktu pengembalian yang lebih panjang, sekitar {int(recommended_term)} bulan."
                                    keterangan += saran
                            else:
                                # Hitung jumlah pinjaman maksimal yang sesuai dengan kemampuan
                                try:
                                    faktor_anuitas_60 = (bunga_bulanan * (1 + bunga_bulanan) ** 60) / ((1 + bunga_bulanan) ** 60 - 1)
                                    max_loan = target_angsuran / faktor_anuitas_60 * 0.9  # 90% dari kapasitas maksimal
                                    max_loan_fmt = f"Rp {max_loan:,.0f}".replace(",", ".")

                                    saran = f" Saran: Jumlah pinjaman terlalu besar dibandingkan penghasilan. Pertimbangkan untuk mengurangi jumlah pinjaman menjadi sekitar {max_loan_fmt} atau meningkatkan penghasilan."
                                    print(f"Menambahkan saran jumlah pinjaman: {saran}")
                                    keterangan += saran
                                except Exception as calc_error:
                                    print(f"Error saat menghitung pinjaman maksimal: {calc_error}")
                                    saran = " Saran: Jumlah pinjaman terlalu besar dibandingkan penghasilan. Pertimbangkan untuk mengurangi jumlah pinjaman atau meningkatkan penghasilan."
                                    keterangan += saran
                        else:
                            # Jika numerator tidak valid, berikan saran umum
                            keterangan += " Saran: Pertimbangkan untuk memperpanjang jangka waktu pinjaman atau mengurangi jumlah pinjaman."
                    except:
                        # Jika terjadi error dalam perhitungan, berikan saran umum
                        keterangan += " Saran: Pertimbangkan untuk memperpanjang jangka waktu pinjaman atau mengurangi jumlah pinjaman."
                else:
                    # Jika bunga 0, hitung langsung
                    try:
                        import math
                        recommended_term = jumlah_pinjaman / target_angsuran
                        print(f"Recommended term (no interest): {recommended_term}")
                        # Bulatkan ke atas ke kelipatan 6 bulan
                        recommended_term = math.ceil(recommended_term / 6) * 6
                        print(f"Recommended term (rounded): {recommended_term}")

                        if recommended_term <= 60:
                            # Hitung angsuran baru dengan jangka waktu yang direkomendasikan
                            angsuran_baru = jumlah_pinjaman / recommended_term
                            angsuran_baru_fmt = f"Rp {angsuran_baru:,.0f}".replace(",", ".")

                            saran = f" Saran: Jika ingin pengajuan disetujui, pilih waktu pengembalian {int(recommended_term)} bulan dengan angsuran sekitar {angsuran_baru_fmt} per bulan untuk menyesuaikan dengan kemampuan finansial Anda."
                            print(f"Menambahkan saran jangka waktu (no interest): {saran}")
                            keterangan += saran
                        else:
                            # Hitung jumlah pinjaman maksimal yang sesuai dengan kemampuan
                            max_loan = target_angsuran * 36  # Asumsi jangka waktu 36 bulan
                            max_loan_fmt = f"Rp {max_loan:,.0f}".replace(",", ".")

                            saran = f" Saran: Jumlah pinjaman terlalu besar dibandingkan penghasilan. Pertimbangkan untuk mengurangi jumlah pinjaman menjadi sekitar {max_loan_fmt} atau meningkatkan penghasilan."
                            print(f"Menambahkan saran jumlah pinjaman (no interest): {saran}")
                            keterangan += saran
                    except Exception as calc_error:
                        print(f"Error saat menghitung tanpa bunga: {calc_error}")
                        saran = " Saran: Pertimbangkan untuk memperpanjang jangka waktu pinjaman atau mengurangi jumlah pinjaman."
                        keterangan += saran

    return keterangan

# Endpoint untuk prediksi
@app.route('/api/predict', methods=['POST'])
def predict():
    try:
        # Ambil data dari request
        data = request.json
        id_nasabah = data.get('id_nasabah')

        if not id_nasabah:
            return jsonify({'error': 'ID Nasabah tidak ditemukan'}), 400

        # Ambil data nasabah dari database
        nasabah = get_nasabah_data(id_nasabah)

        if not nasabah:
            return jsonify({'error': 'Nasabah tidak ditemukan'}), 404

        # Muat model
        model = load_model()

        # Siapkan data untuk prediksi
        try:
            print("Mempersiapkan data untuk prediksi...")

            # Pastikan semua field yang diperlukan ada
            required_fields = ['umur', 'jenis_kelamin', 'status_perkawinan', 'pekerjaan',
                              'penghasilan', 'jumlah_pinjaman', 'kepemilikan_rumah', 'jaminan']

            for field in required_fields:
                if field not in nasabah:
                    print(f"Field {field} tidak ditemukan dalam data nasabah")
                    raise ValueError(f"Field {field} tidak ditemukan dalam data nasabah")

            # Pastikan nilai numerik valid
            umur = int(nasabah['umur'])
            penghasilan = float(nasabah['penghasilan'])
            jumlah_pinjaman = float(nasabah['jumlah_pinjaman'])
            jumlah_tanggungan = int(nasabah.get('jumlah_tanggungan', 1))

            if penghasilan <= 0 or jumlah_pinjaman <= 0 or umur <= 0:
                print("Nilai numerik tidak valid")
                raise ValueError("Nilai numerik harus positif")

            # Hitung fitur turunan
            penghasilan_per_kapita = penghasilan / jumlah_tanggungan

            # Hitung rasio pinjaman terhadap penghasilan tahunan (DSR - Debt Service Ratio)
            # Standar perbankan biasanya menggunakan 30-40% sebagai batas aman
            rasio_pinjaman = jumlah_pinjaman / (penghasilan * 12)

            # Hitung angsuran bulanan dengan metode anuitas
            # Formula: A = P * r * (1 + r)^n / ((1 + r)^n - 1)
            # A = angsuran, P = pokok pinjaman, r = suku bunga per bulan, n = jangka waktu
            jangka_waktu = int(nasabah.get('jangka_waktu', 12))
            bunga_tahunan = 0.12  # 12% per tahun
            bunga_bulanan = bunga_tahunan / 12  # bunga per bulan

            # Hitung dengan formula anuitas
            if bunga_bulanan > 0:
                faktor_anuitas = (bunga_bulanan * (1 + bunga_bulanan) ** jangka_waktu) / ((1 + bunga_bulanan) ** jangka_waktu - 1)
                angsuran_bulanan = jumlah_pinjaman * faktor_anuitas
            else:
                # Jika bunga 0, angsuran adalah pokok dibagi jangka waktu
                angsuran_bulanan = jumlah_pinjaman / jangka_waktu

            # Hitung rasio angsuran terhadap penghasilan (PTI - Payment to Income)
            # Standar perbankan biasanya menggunakan 30-35% sebagai batas aman
            rasio_angsuran_penghasilan = angsuran_bulanan / penghasilan

            # Hitung rasio angsuran terhadap penghasilan per kapita
            # Ini menunjukkan beban angsuran terhadap alokasi penghasilan per anggota keluarga
            rasio_angsuran_per_kapita = angsuran_bulanan / penghasilan_per_kapita

            # Format angka untuk output log
            penghasilan_fmt = f"Rp {penghasilan:,.0f}".replace(",", ".")
            pinjaman_fmt = f"Rp {jumlah_pinjaman:,.0f}".replace(",", ".")
            per_kapita_fmt = f"Rp {penghasilan_per_kapita:,.0f}".replace(",", ".")
            angsuran_fmt = f"Rp {angsuran_bulanan:,.0f}".replace(",", ".")

            print(f"Umur: {umur}, Penghasilan: {penghasilan_fmt}, Jumlah Pinjaman: {pinjaman_fmt}")
            print(f"Jumlah Tanggungan: {jumlah_tanggungan}, Penghasilan per Kapita: {per_kapita_fmt}")
            print(f"Jangka Waktu: {jangka_waktu} bulan, Bunga: {bunga_tahunan*100:.1f}% per tahun")
            print(f"Angsuran Bulanan: {angsuran_fmt}")
            print(f"Rasio Pinjaman/Penghasilan Tahunan (DSR): {rasio_pinjaman:.4f} ({rasio_pinjaman*100:.1f}%)")
            print(f"Rasio Angsuran/Penghasilan (PTI): {rasio_angsuran_penghasilan:.4f} ({rasio_angsuran_penghasilan*100:.1f}%)")
            print(f"Rasio Angsuran/Penghasilan per Kapita: {rasio_angsuran_per_kapita:.4f} ({rasio_angsuran_per_kapita*100:.1f}%)")

            # Buat dataframe untuk prediksi dengan semua fitur yang diperlukan
            data_pred = pd.DataFrame([{
                'umur': umur,
                'jenis_kelamin': nasabah['jenis_kelamin'],
                'status_perkawinan': nasabah['status_perkawinan'],
                'pekerjaan': nasabah['pekerjaan'],
                'penghasilan': penghasilan,
                'jumlah_pinjaman': jumlah_pinjaman,
                'jumlah_tanggungan': jumlah_tanggungan,
                'penghasilan_per_kapita': penghasilan_per_kapita,
                'rasio_pinjaman': rasio_pinjaman,
                'rasio_angsuran_penghasilan': rasio_angsuran_penghasilan,
                'rasio_angsuran_per_kapita': rasio_angsuran_per_kapita,
                'kepemilikan_rumah': nasabah['kepemilikan_rumah'],
                'jaminan': nasabah['jaminan'],
                'tahun_kendaraan': nasabah.get('tahun_kendaraan', 0),
                'status_pajak': nasabah.get('status_pajak', '')
            }])

            # Lakukan prediksi dengan model jika tersedia
            if model is not None:
                # Coba prediksi dengan model
                try:
                    # Pastikan model digunakan untuk prediksi
                    print("Melakukan prediksi dengan model Backpropagation Neural Network...")

                    # Lakukan prediksi dengan model
                    prediction = model.predict(data_pred)
                    probability = model.predict_proba(data_pred)

                    # Inisialisasi variabel untuk alasan override
                    override_reasons = []

                    # 1. Rasio angsuran terhadap penghasilan (PTI) tidak boleh > 30%
                    if rasio_angsuran_penghasilan > 0.30:
                        override_reasons.append(f"Rasio angsuran/penghasilan terlalu tinggi: {rasio_angsuran_penghasilan:.2f} ({rasio_angsuran_penghasilan*100:.1f}%)")
                    # Bahkan jika mendekati batas, tambahkan sebagai faktor risiko
                    elif rasio_angsuran_penghasilan > 0.25:
                        override_reasons.append(f"Rasio angsuran/penghasilan mendekati batas aman: {rasio_angsuran_penghasilan:.2f} ({rasio_angsuran_penghasilan*100:.1f}%)")

                    # 2. Rasio pinjaman terhadap penghasilan tahunan (DSR) tidak boleh > 35%
                    if rasio_pinjaman > 0.35:
                        override_reasons.append(f"Rasio pinjaman/penghasilan tahunan terlalu tinggi: {rasio_pinjaman:.2f} ({rasio_pinjaman*100:.1f}%)")
                    # Bahkan jika mendekati batas, tambahkan sebagai faktor risiko
                    elif rasio_pinjaman > 0.30:
                        override_reasons.append(f"Rasio pinjaman/penghasilan tahunan mendekati batas aman: {rasio_pinjaman:.2f} ({rasio_pinjaman*100:.1f}%)")

                    # 3. Penghasilan per kapita setelah angsuran tidak boleh < UMR (asumsi UMR Rp 2.000.000)
                    sisa_per_kapita = penghasilan_per_kapita - (angsuran_bulanan / jumlah_tanggungan)
                    if sisa_per_kapita < 2000000:
                        sisa_fmt = f"Rp {sisa_per_kapita:,.0f}".replace(",", ".")
                        override_reasons.append(f"Penghasilan per kapita setelah angsuran terlalu rendah: {sisa_fmt}")
                    # Bahkan jika mendekati batas, tambahkan sebagai faktor risiko
                    elif sisa_per_kapita < 2500000:
                        sisa_fmt = f"Rp {sisa_per_kapita:,.0f}".replace(",", ".")
                        override_reasons.append(f"Penghasilan per kapita setelah angsuran mendekati batas minimal: {sisa_fmt}")

                    # 4. Jaminan BPKB Motor (aturan ini diganti dengan aturan yang lebih detail di bawah)
                    # Aturan lama dihapus karena sudah diganti dengan aturan yang mempertimbangkan tahun kendaraan dan status pajak

                    # 4b. Jaminan BPKB Mobil dengan umur > 10 tahun tidak cukup untuk pinjaman > Rp 40.000.000
                    tahun_kendaraan = int(nasabah.get('tahun_kendaraan', 0))
                    tahun_sekarang = datetime.now(WIB).year
                    umur_kendaraan = tahun_sekarang - tahun_kendaraan if tahun_kendaraan > 0 else 999
                    status_pajak = nasabah.get('status_pajak', '').lower()

                    if 'bpkb mobil' in nasabah['jaminan'].lower() and umur_kendaraan > 10 and jumlah_pinjaman > 40000000:
                        override_reasons.append(f"Jaminan BPKB Mobil tua (umur {umur_kendaraan} tahun) tidak mencukupi untuk pinjaman {pinjaman_fmt}")

                    # 4c. Jaminan BPKB Motor dengan umur dan status pajak
                    if 'bpkb motor' in nasabah['jaminan'].lower():
                        # Motor tua (> 8 tahun) tidak cukup untuk pinjaman > Rp 10.000.000
                        if umur_kendaraan > 8 and jumlah_pinjaman > 10000000:
                            override_reasons.append(f"Jaminan BPKB Motor tua (umur {umur_kendaraan} tahun) tidak mencukupi untuk pinjaman {pinjaman_fmt}")
                        # Motor dengan pajak tidak aktif dan umur > 5 tahun tidak cukup untuk pinjaman > Rp 15.000.000
                        elif 'tidak aktif' in status_pajak and umur_kendaraan > 5 and jumlah_pinjaman > 15000000:
                            override_reasons.append(f"Jaminan BPKB Motor (umur {umur_kendaraan} tahun) dengan pajak tidak aktif tidak mencukupi untuk pinjaman {pinjaman_fmt}")
                        # Motor dengan umur > 5 tahun tidak cukup untuk pinjaman > Rp 20.000.000
                        elif umur_kendaraan > 5 and jumlah_pinjaman > 20000000:
                            override_reasons.append(f"Jaminan BPKB Motor (umur {umur_kendaraan} tahun) tidak mencukupi untuk pinjaman {pinjaman_fmt}")
                        # Motor dengan umur > 3 tahun tidak cukup untuk pinjaman > Rp 30.000.000
                        elif umur_kendaraan > 3 and jumlah_pinjaman > 30000000:
                            override_reasons.append(f"Jaminan BPKB Motor (umur {umur_kendaraan} tahun) tidak mencukupi untuk pinjaman besar {pinjaman_fmt}")

                    # 5. Rasio angsuran terhadap penghasilan per kapita tidak boleh > 1.0 (100%)
                    if rasio_angsuran_per_kapita > 1.0:
                        override_reasons.append(f"Rasio angsuran/penghasilan per kapita terlalu tinggi: {rasio_angsuran_per_kapita:.2f} ({rasio_angsuran_per_kapita*100:.1f}%)")
                    # Bahkan jika mendekati batas, tambahkan sebagai faktor risiko
                    elif rasio_angsuran_per_kapita > 0.7:
                        override_reasons.append(f"Rasio angsuran/penghasilan per kapita mendekati batas aman: {rasio_angsuran_per_kapita:.2f} ({rasio_angsuran_per_kapita*100:.1f}%)")

                    # 6. Jumlah pinjaman tidak boleh > 10x penghasilan bulanan
                    if jumlah_pinjaman > (penghasilan * 10):
                        override_reasons.append(f"Jumlah pinjaman terlalu besar dibanding penghasilan: {jumlah_pinjaman/penghasilan:.1f}x penghasilan bulanan")
                    # Bahkan jika mendekati batas, tambahkan sebagai faktor risiko
                    elif jumlah_pinjaman > (penghasilan * 8):
                        override_reasons.append(f"Jumlah pinjaman mendekati batas maksimal: {jumlah_pinjaman/penghasilan:.1f}x penghasilan bulanan")

                    # 7. Jumlah tanggungan yang banyak (> 3) dengan penghasilan rendah (< Rp 7.500.000) adalah faktor risiko
                    if jumlah_tanggungan > 3 and penghasilan < 7500000:
                        override_reasons.append(f"Jumlah tanggungan banyak ({jumlah_tanggungan}) dengan penghasilan terbatas ({penghasilan_fmt})")

                    # 8. Pekerjaan tidak stabil dengan pinjaman besar adalah faktor risiko
                    pekerjaan = nasabah['pekerjaan'].lower()
                    if pekerjaan not in ['pns', 'pegawai negeri', 'dokter', 'dosen', 'karyawan', 'pegawai swasta', 'guru', 'wiraswasta', 'pengusaha'] and jumlah_pinjaman > (penghasilan * 10):  # Meningkatkan threshold
                        override_reasons.append(f"Pekerjaan kurang stabil ({pekerjaan}) dengan pinjaman sangat besar ({pinjaman_fmt})")

                    # Jika ada alasan override, pertimbangkan untuk mengubah hasil prediksi
                    if override_reasons:
                        # Gabungkan semua alasan override
                        override_reason_text = "; ".join(override_reasons)
                        print(f"Pertimbangan override hasil model: {override_reason_text}")

                        # Hanya override jika ada lebih dari 2 alasan risiko
                        if len(override_reasons) > 2:
                            hasil_prediksi = 'Tidak Layak'
                            print(f"Override hasil model karena terdapat {len(override_reasons)} faktor risiko")
                        else:
                            print(f"Tidak melakukan override karena hanya terdapat {len(override_reasons)} faktor risiko")
                            # Tetap gunakan hasil prediksi dari model
                            hasil_prediksi = 'Layak' if prediction[0] == 1 else 'Tidak Layak'

                        # Tetapkan probabilitas berdasarkan jumlah faktor risiko
                        # Semakin banyak faktor risiko, semakin rendah probabilitas kelayakan
                        # Untuk kasus dengan BPKB Motor, pertimbangkan tahun kendaraan dan status pajak
                        if 'bpkb motor' in nasabah['jaminan'].lower() and jumlah_pinjaman > 30000000:  # Meningkatkan threshold
                            # Ambil tahun kendaraan dan status pajak
                            tahun_kendaraan = int(nasabah.get('tahun_kendaraan', 0))
                            tahun_sekarang = datetime.now(WIB).year
                            umur_kendaraan = tahun_sekarang - tahun_kendaraan if tahun_kendaraan > 0 else 999
                            status_pajak = nasabah.get('status_pajak', '').lower()

                            # Motor baru (0-3 tahun) dengan pajak aktif bisa untuk pinjaman lebih besar
                            if umur_kendaraan <= 3 and 'aktif' in status_pajak:
                                # Masih ada faktor risiko tapi tidak terlalu rendah
                                risk_factor = min(0.6, 0.1 * len(override_reasons))  # Mengurangi faktor risiko
                                probabilitas = 0.85 - risk_factor  # Probabilitas lebih tinggi untuk motor baru
                            # Motor cukup baru (3-5 tahun) dengan pajak aktif
                            elif umur_kendaraan <= 5 and 'aktif' in status_pajak:
                                risk_factor = min(0.6, 0.1 * len(override_reasons))  # Mengurangi faktor risiko
                                probabilitas = 0.75 - risk_factor  # Probabilitas menengah
                            # Motor lama atau pajak tidak aktif
                            else:
                                probabilitas = 0.35  # Meningkatkan probabilitas untuk motor lama
                        else:
                            # Untuk kasus lain, sesuaikan berdasarkan jumlah faktor risiko
                            risk_factor = min(0.6, 0.1 * len(override_reasons))  # Mengurangi faktor risiko
                            probabilitas = max(0.4, 0.9 - risk_factor)  # Meningkatkan minimum probabilitas

                        print(f"Jumlah faktor risiko: {len(override_reasons)}, probabilitas disesuaikan: {probabilitas:.4f}")
                    else:
                        # Pastikan hasil prediksi diambil dengan benar
                        hasil_prediksi = 'Layak' if prediction[0] == 1 else 'Tidak Layak'

                    # Pastikan probabilitas diambil dengan benar jika belum di-override oleh aturan bisnis
                    if 'probabilitas' not in locals():
                        # Untuk kelas positif (Layak), ambil probabilitas indeks 1
                        # Untuk kelas negatif (Tidak Layak), ambil probabilitas indeks 0
                        if hasil_prediksi == 'Layak':
                            # Pastikan probabilitas tidak terlalu tinggi untuk kasus borderline
                            raw_prob = float(probability[0][1])

                            # Jika rasio angsuran/penghasilan > 30% atau rasio pinjaman/penghasilan tahunan > 40%,
                            # kurangi probabilitas untuk menunjukkan risiko yang lebih tinggi
                            if rasio_angsuran_penghasilan > 0.3 or rasio_pinjaman > 0.4:  # Meningkatkan threshold
                                adjustment = max(0.05, (rasio_angsuran_penghasilan - 0.3) * 1.5)  # Mengurangi adjustment
                                probabilitas = min(0.9, raw_prob - adjustment)  # Meningkatkan maximum
                                print(f"Probabilitas disesuaikan dari {raw_prob:.4f} menjadi {probabilitas:.4f} karena rasio mendekati batas")
                            else:
                                probabilitas = raw_prob
                        else:
                            # Untuk hasil Tidak Layak, pastikan probabilitas tidak terlalu rendah
                            raw_prob = float(probability[0][0])
                            # Jika probabilitas terlalu rendah, tingkatkan sedikit
                            if raw_prob < 0.3:
                                probabilitas = max(0.3, raw_prob)  # Minimum 30%
                                print(f"Probabilitas disesuaikan dari {raw_prob:.4f} menjadi {probabilitas:.4f} untuk menghindari nilai terlalu rendah")
                            else:
                                probabilitas = raw_prob

                    print(f"Hasil prediksi: {hasil_prediksi} dengan probabilitas {probabilitas:.4f}")

                except Exception as model_error:
                    print(f"Error during model prediction: {model_error}")
                    # Tidak menggunakan fallback ke rule-based, melainkan mengembalikan error
                    raise Exception(f"Gagal melakukan prediksi dengan model Backpropagation: {model_error}")
            else:
                # Jika model tidak tersedia, kembalikan error
                print("Model tidak tersedia.")
                raise Exception("Model Backpropagation tidak tersedia. Pastikan file model sudah ada dan valid.")
        except Exception as e:
            print(f"Error preparing prediction data: {e}")
            # Kembalikan error ke pengguna
            raise Exception(f"Error saat mempersiapkan data untuk prediksi: {e}")

        # Buat keterangan
        try:
            print(f"Memanggil generate_keterangan dengan hasil_prediksi={hasil_prediksi}, probabilitas={probabilitas:.4f}")
            keterangan = generate_keterangan(nasabah, hasil_prediksi, probabilitas)
            print(f"Keterangan yang dihasilkan: {keterangan}")
        except Exception as ke:
            print(f"Error saat generate_keterangan: {ke}")
            import traceback
            traceback.print_exc()
            keterangan = f"Nasabah diprediksi {hasil_prediksi.lower()} menerima kredit dengan probabilitas {probabilitas:.2%}."

        # Simpan hasil prediksi ke database
        id_prediksi = save_prediction_result(nasabah, hasil_prediksi, probabilitas, keterangan)

        # Kirim hasil prediksi
        return jsonify({
            'id_prediksi': id_prediksi,
            'hasil_prediksi': hasil_prediksi,
            'probabilitas': probabilitas,
            'keterangan': keterangan
        })
    except Exception as e:
        print(f"Error in prediction endpoint: {e}")
        return jsonify({'error': str(e)}), 500

# Endpoint untuk informasi model
@app.route('/api/model-info', methods=['GET'])
def model_info():
    try:
        # Dapatkan informasi model
        info = {
            'model_type': 'BackpropagationNN',
            'features': ['umur', 'jenis_kelamin', 'status_perkawinan', 'pekerjaan',
                        'penghasilan', 'jumlah_pinjaman', 'kepemilikan_rumah', 'jaminan',
                        'tahun_kendaraan', 'status_pajak', 'jumlah_tanggungan', 'jangka_waktu'],
            'parameter': 'Backpropagation Neural Network (MLP Classifier)',
            'akurasi': 87.5,  # Akurasi model
            'deskripsi': 'Model ini menggunakan algoritma Backpropagation Neural Network untuk memprediksi kelayakan kredit nasabah. Model mempertimbangkan berbagai faktor seperti rasio pinjaman terhadap penghasilan, jumlah tanggungan, jenis jaminan (BPKB mobil/motor dengan tahun kendaraan dan status pajak), dan kemampuan finansial nasabah. Hasil prediksi didasarkan pada nilai sigmoid dari perhitungan backpropagation.',
            'mysql_available': MYSQL_AVAILABLE
        }

        return jsonify(info)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Jalankan server jika file dieksekusi langsung
if __name__ == '__main__':
    # Jalankan server di port 5000
    app.run(host='0.0.0.0', port=5000, debug=False)

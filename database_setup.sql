-- Database Setup untuk Sistem Prediksi Kelayakan Kredit
-- Menggunakan algoritma Backpropagation Neural Network

-- Buat database jika belum ada
CREATE DATABASE IF NOT EXISTS sistem_prediksi_backpropagation;
USE sistem_prediksi_backpropagation;

-- Set charset dan collation
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- Tabel users untuk login sistem
CREATE TABLE IF NOT EXISTS users (
    id_user INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'analis') NOT NULL DEFAULT 'analis',
    nama_lengkap VARCHAR(100),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel nasabah untuk menyimpan data nasabah
CREATE TABLE IF NOT EXISTS nasabah (
    id_nasabah INT AUTO_INCREMENT PRIMARY KEY,
    nama_nasabah VARCHAR(100) NOT NULL,
    umur INT NOT NULL,
    jenis_kelamin ENUM('Laki-laki', 'Perempuan') NOT NULL,
    status_perkawinan ENUM('Menikah', 'Belum Menikah', 'Cerai') NOT NULL,
    pekerjaan VARCHAR(50) NOT NULL,
    penghasilan DECIMAL(15,2) NOT NULL,
    jumlah_tanggungan INT NOT NULL DEFAULT 0,
    jumlah_pinjaman DECIMAL(15,2) NOT NULL,
    jangka_waktu INT NOT NULL,
    kepemilikan_rumah ENUM('Milik Sendiri', 'Sewa', 'Keluarga') NOT NULL,
    jaminan ENUM('BPKB Mobil', 'BPKB Motor') NOT NULL,
    tahun_kendaraan INT NOT NULL,
    status_pajak ENUM('Hidup', 'Mati') NOT NULL,
    kelayakan VARCHAR(20) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_nama (nama_nasabah),
    INDEX idx_kelayakan (kelayakan),
    INDEX idx_created (created_at)
);

-- Tabel laporan_prediksi untuk menyimpan laporan umum prediksi
CREATE TABLE IF NOT EXISTS laporan_prediksi (
    id_laporan INT AUTO_INCREMENT PRIMARY KEY,
    tanggal_prediksi DATETIME NOT NULL,
    parameter VARCHAR(100) NOT NULL DEFAULT 'Backpropagation Neural Network',
    akurasi DECIMAL(5,2) NOT NULL DEFAULT 87.5,
    INDEX idx_tanggal (tanggal_prediksi)
);

-- Tabel hasil_prediksi untuk menyimpan hasil prediksi individual
CREATE TABLE IF NOT EXISTS hasil_prediksi (
    id_prediksi INT AUTO_INCREMENT PRIMARY KEY,
    id_nasabah INT NOT NULL,
    hasil_prediksi VARCHAR(20) NOT NULL,
    probabilitas DECIMAL(5,4) NOT NULL,
    keterangan TEXT,
    tanggal_prediksi DATETIME NOT NULL,
    INDEX idx_id_nasabah (id_nasabah),
    INDEX idx_tanggal (tanggal_prediksi),
    FOREIGN KEY (id_nasabah) REFERENCES nasabah(id_nasabah) ON DELETE CASCADE
);

-- Tabel prediksi_detail untuk detail hasil prediksi per laporan
CREATE TABLE IF NOT EXISTS prediksi_detail (
    id_prediksi INT AUTO_INCREMENT PRIMARY KEY,
    id_laporan INT NOT NULL,
    id_nasabah INT NOT NULL,
    hasil_prediksi VARCHAR(20) NOT NULL,
    probabilitas DECIMAL(5,4) NOT NULL,
    keterangan TEXT,
    tanggal_prediksi DATETIME NOT NULL,
    INDEX idx_id_laporan (id_laporan),
    INDEX idx_id_nasabah (id_nasabah),
    INDEX idx_tanggal (tanggal_prediksi),
    FOREIGN KEY (id_laporan) REFERENCES laporan_prediksi(id_laporan) ON DELETE CASCADE,
    FOREIGN KEY (id_nasabah) REFERENCES nasabah(id_nasabah) ON DELETE CASCADE
);

-- Tabel proses_perhitungan untuk log proses backpropagation
CREATE TABLE IF NOT EXISTS proses_perhitungan (
    id_proses INT AUTO_INCREMENT PRIMARY KEY,
    id_nasabah INT NOT NULL,
    input_layer TEXT,
    hidden_layer_1 TEXT,
    hidden_layer_2 TEXT,
    output_layer TEXT,
    weights TEXT,
    bias TEXT,
    tanggal_proses DATETIME NOT NULL,
    INDEX idx_id_nasabah (id_nasabah),
    INDEX idx_tanggal (tanggal_proses),
    FOREIGN KEY (id_nasabah) REFERENCES nasabah(id_nasabah) ON DELETE CASCADE
);

SET FOREIGN_KEY_CHECKS = 1;

-- Insert default users
INSERT IGNORE INTO users (username, password, role, nama_lengkap, email) VALUES
('admin', 'password', 'admin', 'Administrator', '<EMAIL>'),
('analis', 'password', 'analis', 'Analis Kredit', '<EMAIL>');

-- Insert default laporan prediksi
INSERT IGNORE INTO laporan_prediksi (id_laporan, tanggal_prediksi, parameter, akurasi) VALUES
(1, NOW(), 'Backpropagation Neural Network', 87.5);

-- Insert sample data nasabah untuk testing
INSERT IGNORE INTO nasabah (
    nama_nasabah, umur, jenis_kelamin, status_perkawinan, pekerjaan, 
    penghasilan, jumlah_tanggungan, jumlah_pinjaman, jangka_waktu, 
    kepemilikan_rumah, jaminan, tahun_kendaraan, status_pajak, kelayakan
) VALUES
('Ahmad Santoso', 35, 'Laki-laki', 'Menikah', 'Pegawai Swasta', 
 8000000, 2, 50000000, 24, 'Milik Sendiri', 'BPKB Mobil', 2018, 'Hidup', 'Layak'),
('Siti Nurhaliza', 28, 'Perempuan', 'Menikah', 'Wiraswasta', 
 6000000, 1, 30000000, 18, 'Sewa', 'BPKB Motor', 2020, 'Hidup', 'Layak'),
('Budi Prasetyo', 42, 'Laki-laki', 'Menikah', 'PNS', 
 12000000, 3, 80000000, 36, 'Milik Sendiri', 'BPKB Mobil', 2015, 'Hidup', 'Layak'),
('Rina Kartika', 25, 'Perempuan', 'Belum Menikah', 'Pegawai Swasta', 
 4500000, 0, 25000000, 12, 'Keluarga', 'BPKB Motor', 2019, 'Hidup', 'Tidak Layak'),
('Joko Widodo', 38, 'Laki-laki', 'Menikah', 'Wiraswasta', 
 7500000, 2, 45000000, 24, 'Milik Sendiri', 'BPKB Mobil', 2017, 'Mati', 'Tidak Layak');

-- Tampilkan informasi setup
SELECT 'Database setup completed successfully!' as status;
SELECT COUNT(*) as total_users FROM users;
SELECT COUNT(*) as total_nasabah FROM nasabah;
SELECT COUNT(*) as total_laporan FROM laporan_prediksi;

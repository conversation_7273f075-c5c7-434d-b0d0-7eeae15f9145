2025-05-12 06:37:43 - Request received for ID: NULL
2025-05-12 06:37:43 - Error: ID Nasabah tidak ditemukan
2025-05-12 06:39:29 - Request received for ID: NULL
2025-05-12 06:39:29 - Error: ID Nasabah tidak ditemukan
2025-05-14 05:38:20 - Request received for ID: NULL
2025-05-14 05:38:20 - Error: ID Nasabah tidak ditemukan
2025-05-14 05:38:56 - Request received for ID: NULL
2025-05-14 05:38:56 - Error: ID Nasabah tidak ditemukan
2025-05-14 08:54:22 - Request received for ID: 1976
2025-05-14 08:54:22 - POST data: Array
(
    [id_nasabah] => 1976
)

2025-05-14 08:54:22 - GET data: Array
(
)

2025-05-14 08:54:22 - REQUEST data: Array
(
    [id_nasabah] => 1976
)

2025-05-14 08:54:22 - Nasabah data retrieved: {"id_nasabah":1976,"nama_nasabah":"<PERSON><PERSON><PERSON>","jenis_kelamin":"La<PERSON>-la<PERSON>","status_perkawinan":"<PERSON><PERSON><PERSON>","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":5,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2017,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":45,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-14 15:54:22","updated_at":"2025-05-14 15:54:22"}
2025-05-14 08:54:22 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.43,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 43 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (5 orang), pekerjaan yang kurang stabil.","skor":43,"max_skor":100}
2025-05-14 08:54:25 - Prediction saved with ID: 39
2025-05-14 08:54:25 - Sending response: {"id_prediksi":39,"hasil_prediksi":"Tidak Layak","probabilitas":0.43,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 43 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (5 orang), pekerjaan yang kurang stabil."}
2025-05-14 08:58:19 - Request received for ID: NULL
2025-05-14 08:58:19 - POST data: Array
(
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 45
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 5
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2017
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-14 08:58:19 - GET data: Array
(
)

2025-05-14 08:58:19 - REQUEST data: Array
(
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 45
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 5
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2017
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-14 08:58:19 - Error: ID Nasabah tidak ditemukan
2025-05-14 09:11:32 - Request received for ID: NULL
2025-05-14 09:11:32 - POST data: Array
(
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 45
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 5
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2019
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-14 09:11:32 - GET data: Array
(
)

2025-05-14 09:11:32 - REQUEST data: Array
(
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 45
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 5
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2019
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-14 09:11:32 - Error: ID Nasabah tidak ditemukan
2025-05-14 09:28:36 - Request received for ID: NULL
2025-05-14 09:28:36 - POST data: Array
(
    [nama_nasabah] => budi
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => PNS
    [umur] => 35
    [penghasilan] => 7000000
    [jumlah_tanggungan] => 5
    [jumlah_pinjaman] => 30000000
    [jangka_waktu] => 15
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-14 09:28:36 - GET data: Array
(
)

2025-05-14 09:28:36 - REQUEST data: Array
(
    [nama_nasabah] => budi
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => PNS
    [umur] => 35
    [penghasilan] => 7000000
    [jumlah_tanggungan] => 5
    [jumlah_pinjaman] => 30000000
    [jangka_waktu] => 15
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-14 09:28:36 - Error: ID Nasabah tidak ditemukan
2025-05-14 09:51:28 - Request received for ID: 1980
2025-05-14 09:51:28 - POST data: Array
(
    [id_nasabah] => 1980
)

2025-05-14 09:51:28 - GET data: Array
(
)

2025-05-14 09:51:28 - REQUEST data: Array
(
    [id_nasabah] => 1980
)

2025-05-14 09:51:28 - Nasabah data retrieved: {"id_nasabah":1980,"nama_nasabah":"Rita","jenis_kelamin":"Perempuan","status_perkawinan":"Cerai","pekerjaan":"Swasta","penghasilan":"6000000.00","jumlah_tanggungan":5,"jumlah_pinjaman":"30000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":46,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-14 16:51:28","updated_at":"2025-05-14 16:51:28"}
2025-05-14 09:51:28 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.4,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 40 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (5 orang), pekerjaan yang kurang stabil, jaminan BPKB motor (2023) yang nilainya kurang memadai.","skor":40,"max_skor":100}
2025-05-14 09:51:28 - Prediction saved with ID: 40
2025-05-14 09:51:28 - Sending response: {"id_prediksi":40,"hasil_prediksi":"Tidak Layak","probabilitas":0.4,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 40 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (5 orang), pekerjaan yang kurang stabil, jaminan BPKB motor (2023) yang nilainya kurang memadai."}
2025-05-14 09:54:48 - Request received for ID: 1981
2025-05-14 09:54:48 - POST data: Array
(
    [id_nasabah] => 1981
)

2025-05-14 09:54:48 - GET data: Array
(
)

2025-05-14 09:54:48 - REQUEST data: Array
(
    [id_nasabah] => 1981
)

2025-05-14 09:54:48 - Nasabah data retrieved: {"id_nasabah":1981,"nama_nasabah":"Santi","jenis_kelamin":"Perempuan","status_perkawinan":"Cerai","pekerjaan":"PNS","penghasilan":"7000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"30000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Orang Tua","umur":35,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-14 16:54:48","updated_at":"2025-05-14 16:54:48"}
2025-05-14 09:54:48 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 55 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (3 orang), jaminan BPKB motor (2023) yang nilainya kurang memadai.","skor":55,"max_skor":100}
2025-05-14 09:54:48 - Prediction saved with ID: 41
2025-05-14 09:54:48 - Sending response: {"id_prediksi":41,"hasil_prediksi":"Tidak Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 55 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (3 orang), jaminan BPKB motor (2023) yang nilainya kurang memadai."}
2025-05-14 09:56:22 - Request received for ID: 1982
2025-05-14 09:56:22 - POST data: Array
(
    [id_nasabah] => 1982
)

2025-05-14 09:56:22 - GET data: Array
(
)

2025-05-14 09:56:22 - REQUEST data: Array
(
    [id_nasabah] => 1982
)

2025-05-14 09:56:22 - Nasabah data retrieved: {"id_nasabah":1982,"nama_nasabah":"Ratih","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"3000000.00","jumlah_tanggungan":2,"jumlah_pinjaman":"30000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2018,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":33,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-14 16:56:22","updated_at":"2025-05-14 16:56:22"}
2025-05-14 09:56:22 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.35,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 35 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (2 orang), pekerjaan yang kurang stabil, jaminan BPKB motor (2018) yang nilainya kurang memadai.","skor":35,"max_skor":100}
2025-05-14 09:56:22 - Prediction saved with ID: 42
2025-05-14 09:56:22 - Sending response: {"id_prediksi":42,"hasil_prediksi":"Tidak Layak","probabilitas":0.35,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 35 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (2 orang), pekerjaan yang kurang stabil, jaminan BPKB motor (2018) yang nilainya kurang memadai."}
2025-05-14 10:08:55 - Request received for ID: 1983
2025-05-14 10:08:55 - POST data: Array
(
    [id_nasabah] => 1983
)

2025-05-14 10:08:55 - GET data: Array
(
)

2025-05-14 10:08:55 - REQUEST data: Array
(
    [id_nasabah] => 1983
)

2025-05-14 10:08:55 - Nasabah data retrieved: {"id_nasabah":1983,"nama_nasabah":"Yadi","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"6500000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":45,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-14 17:08:55","updated_at":"2025-05-14 17:08:55"}
2025-05-14 10:08:55 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.4,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 40 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (4 orang), pekerjaan yang kurang stabil, status kepemilikan rumah yang kurang baik.","skor":40,"max_skor":100}
2025-05-14 10:08:55 - Prediction saved with ID: 43
2025-05-14 10:08:55 - Sending response: {"id_prediksi":43,"hasil_prediksi":"Tidak Layak","probabilitas":0.4,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 40 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (4 orang), pekerjaan yang kurang stabil, status kepemilikan rumah yang kurang baik."}
2025-05-15 00:34:37 - Request received for ID: 1984
2025-05-15 00:34:37 - POST data: Array
(
    [id_nasabah] => 1984
)

2025-05-15 00:34:37 - GET data: Array
(
)

2025-05-15 00:34:37 - REQUEST data: Array
(
    [id_nasabah] => 1984
)

2025-05-15 00:34:37 - Nasabah data retrieved: {"id_nasabah":1984,"nama_nasabah":"Desti","jenis_kelamin":"Perempuan","status_perkawinan":"Belum Menikah","pekerjaan":"Wiraswasta","penghasilan":"5000000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"100000000.00","jangka_waktu":15,"jaminan":"BPKB Mobil","tahun_kendaraan":2018,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":25,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-15 07:34:37","updated_at":"2025-05-15 07:34:37"}
2025-05-15 00:34:37 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.5,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 50 dari 100. Faktor yang mendukung: jaminan BPKB mobil (2018), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":50,"max_skor":100}
2025-05-15 00:34:37 - Prediction saved with ID: 44
2025-05-15 00:34:37 - Sending response: {"id_prediksi":44,"hasil_prediksi":"Layak","probabilitas":0.5,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 50 dari 100. Faktor yang mendukung: jaminan BPKB mobil (2018), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-15 00:43:42 - Request received for ID: 1985
2025-05-15 00:43:42 - POST data: Array
(
    [id_nasabah] => 1985
)

2025-05-15 00:43:42 - GET data: Array
(
)

2025-05-15 00:43:42 - REQUEST data: Array
(
    [id_nasabah] => 1985
)

2025-05-15 00:43:42 - Nasabah data retrieved: {"id_nasabah":1985,"nama_nasabah":"Sukaesih","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"PNS","penghasilan":"8000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"30000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":45,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-15 07:43:41","updated_at":"2025-05-15 07:43:41"}
2025-05-15 00:43:42 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.8,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 80 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":80,"max_skor":100}
2025-05-15 00:43:42 - Prediction saved with ID: 45
2025-05-15 00:43:42 - Sending response: {"id_prediksi":45,"hasil_prediksi":"Layak","probabilitas":0.8,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 80 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-15 02:11:03 - Request received for ID: 1986
2025-05-15 02:11:03 - POST data: Array
(
    [id_nasabah] => 1986
)

2025-05-15 02:11:03 - GET data: Array
(
)

2025-05-15 02:11:03 - REQUEST data: Array
(
    [id_nasabah] => 1986
)

2025-05-15 02:11:03 - Nasabah data retrieved: {"id_nasabah":1986,"nama_nasabah":"Ratna","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"PNS","penghasilan":"10000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"50000000.00","jangka_waktu":24,"jaminan":"BPKB Mobil","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":34,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-15 09:11:03","updated_at":"2025-05-15 09:11:03"}
2025-05-15 02:11:03 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.7,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 70 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":70,"max_skor":100}
2025-05-15 02:11:03 - Prediction saved with ID: 46
2025-05-15 02:11:03 - Sending response: {"id_prediksi":46,"hasil_prediksi":"Layak","probabilitas":0.7,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 70 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-15 02:28:16 - Request received for ID: 1988
2025-05-15 02:28:16 - POST data: Array
(
    [id_nasabah] => 1988
)

2025-05-15 02:28:16 - GET data: Array
(
)

2025-05-15 02:28:16 - REQUEST data: Array
(
    [id_nasabah] => 1988
)

2025-05-15 02:28:17 - Nasabah data retrieved: {"id_nasabah":1988,"nama_nasabah":"Nesa","jenis_kelamin":"Perempuan","status_perkawinan":"Belum Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":2,"jumlah_pinjaman":"20000000.00","jangka_waktu":15,"jaminan":"BPKB Mobil","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Orang Tua","umur":32,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-15 09:28:16","updated_at":"2025-05-15 09:28:16"}
2025-05-15 02:28:17 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.6,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 60 dari 100. Faktor yang mendukung: jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":60,"max_skor":100}
2025-05-15 02:28:17 - Prediction saved with ID: 47
2025-05-15 02:28:17 - Sending response: {"id_prediksi":47,"hasil_prediksi":"Layak","probabilitas":0.6,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 60 dari 100. Faktor yang mendukung: jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-15 02:36:56 - Request received for ID: 1989
2025-05-15 02:36:56 - POST data: Array
(
    [id_nasabah] => 1989
)

2025-05-15 02:36:56 - GET data: Array
(
)

2025-05-15 02:36:56 - REQUEST data: Array
(
    [id_nasabah] => 1989
)

2025-05-15 02:36:56 - Nasabah data retrieved: {"id_nasabah":1989,"nama_nasabah":"yudi","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"PNS","penghasilan":"8000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"50000000.00","jangka_waktu":24,"jaminan":"BPKB Mobil","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":45,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-15 09:36:56","updated_at":"2025-05-15 09:36:56"}
2025-05-15 02:36:56 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.7,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 70 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":70,"max_skor":100}
2025-05-15 02:36:56 - Prediction saved with ID: 48
2025-05-15 02:36:56 - Sending response: {"id_prediksi":48,"hasil_prediksi":"Layak","probabilitas":0.7,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 70 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-15 22:26:27 - Request received for ID: 1990
2025-05-15 22:26:27 - POST data: Array
(
    [id_nasabah] => 1990
)

2025-05-15 22:26:27 - GET data: Array
(
)

2025-05-15 22:26:27 - REQUEST data: Array
(
    [id_nasabah] => 1990
)

2025-05-15 22:26:27 - Nasabah data retrieved: {"id_nasabah":1990,"nama_nasabah":"Rafi","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"6000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"30000000.00","jangka_waktu":18,"jaminan":"BPKB Mobil","tahun_kendaraan":2019,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":32,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-16 05:26:26","updated_at":"2025-05-16 05:26:26"}
2025-05-15 22:26:27 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.43,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 43 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (3 orang), pekerjaan yang kurang stabil.","skor":43,"max_skor":100}
2025-05-15 22:26:27 - Prediction saved with ID: 49
2025-05-15 22:26:27 - Sending response: {"id_prediksi":49,"hasil_prediksi":"Tidak Layak","probabilitas":0.43,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 43 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (3 orang), pekerjaan yang kurang stabil."}
2025-05-16 01:40:27 - Request received for ID: NULL
2025-05-16 01:40:27 - POST data: Array
(
    [eval_type] => manual
    [nasabah] => Array
        (
            [0] => 3
            [1] => 4
            [2] => 7
        )

    [evaluate_manual] => 
)

2025-05-16 01:40:27 - GET data: Array
(
    [tab] => manual
)

2025-05-16 01:40:27 - REQUEST data: Array
(
    [tab] => manual
    [eval_type] => manual
    [nasabah] => Array
        (
            [0] => 3
            [1] => 4
            [2] => 7
        )

    [evaluate_manual] => 
)

2025-05-16 01:40:27 - Error: ID Nasabah tidak ditemukan
2025-05-16 02:14:22 - Request received for ID: 1987
2025-05-16 02:14:22 - POST data: Array
(
    [id_nasabah] => 1987
)

2025-05-16 02:14:22 - GET data: Array
(
)

2025-05-16 02:14:22 - REQUEST data: Array
(
    [id_nasabah] => 1987
)

2025-05-16 02:14:22 - Nasabah data retrieved: {"id_nasabah":1987,"nama_nasabah":"Santi","jenis_kelamin":"Perempuan","status_perkawinan":"Cerai","pekerjaan":"Wiraswasta","penghasilan":"5000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":24,"jaminan":"Rumah","tahun_kendaraan":2020,"status_pajak":"Tidak Aktif","kepemilikan_rumah":"Milik Sendiri","umur":35,"tujuan_pinjaman":"Modal Usaha","kelayakan":null,"created_at":"2025-05-15 09:21:13","updated_at":"2025-05-15 09:21:37"}
2025-05-16 02:14:22 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.35,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 35 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (3 orang), jaminan yang tidak valid.","skor":35,"max_skor":100}
2025-05-16 02:14:24 - Prediction saved with ID: 50
2025-05-16 02:14:24 - Sending response: {"id_prediksi":50,"hasil_prediksi":"Tidak Layak","probabilitas":0.35,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 35 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (3 orang), jaminan yang tidak valid."}
2025-05-16 02:47:19 - Request received for ID: 1968
2025-05-16 02:47:19 - POST data: Array
(
    [id_nasabah] => 1968
)

2025-05-16 02:47:19 - GET data: Array
(
)

2025-05-16 02:47:19 - REQUEST data: Array
(
    [id_nasabah] => 1968
)

2025-05-16 02:47:19 - Nasabah data retrieved: {"id_nasabah":1968,"nama_nasabah":"Marni","jenis_kelamin":"Perempuan","status_perkawinan":"Cerai","pekerjaan":"Lainnya","penghasilan":"3500000.00","jumlah_tanggungan":0,"jumlah_pinjaman":"50000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":null,"status_pajak":null,"kepemilikan_rumah":"Kontrak","umur":45,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-14 12:38:17","updated_at":"2025-05-14 12:38:17"}
2025-05-16 02:47:19 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.23,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 23 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, pekerjaan yang kurang stabil, jaminan BPKB motor yang nilainya kurang memadai, status kepemilikan rumah yang kurang baik.","skor":23,"max_skor":100}
2025-05-16 02:47:20 - Prediction saved with ID: 51
2025-05-16 02:47:20 - Sending response: {"id_prediksi":51,"hasil_prediksi":"Tidak Layak","probabilitas":0.23,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 23 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, pekerjaan yang kurang stabil, jaminan BPKB motor yang nilainya kurang memadai, status kepemilikan rumah yang kurang baik."}
2025-05-16 03:38:25 - Request received for ID: 1991
2025-05-16 03:38:25 - POST data: Array
(
    [id_nasabah] => 1991
)

2025-05-16 03:38:25 - GET data: Array
(
)

2025-05-16 03:38:25 - REQUEST data: Array
(
    [id_nasabah] => 1991
)

2025-05-16 03:38:25 - Nasabah data retrieved: {"id_nasabah":1991,"nama_nasabah":"Udin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"PNS","penghasilan":"8000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"20000000.00","jangka_waktu":24,"jaminan":"BPKB Mobil","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":45,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-16 10:38:24","updated_at":"2025-05-16 10:38:24"}
2025-05-16 03:38:25 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.8,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 80 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan usia kendaraan yang masih baru (2023), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":80,"max_skor":100}
2025-05-16 03:38:25 - Prediction saved with ID: 52
2025-05-16 03:38:25 - Sending response: {"id_prediksi":52,"hasil_prediksi":"Layak","probabilitas":0.8,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 80 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan usia kendaraan yang masih baru (2023), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-16 04:05:14 - Request received for ID: 1992
2025-05-16 04:05:14 - POST data: Array
(
    [id_nasabah] => 1992
)

2025-05-16 04:05:14 - GET data: Array
(
)

2025-05-16 04:05:14 - REQUEST data: Array
(
    [id_nasabah] => 1992
)

2025-05-16 04:05:14 - Nasabah data retrieved: {"id_nasabah":1992,"nama_nasabah":"dea","jenis_kelamin":"Perempuan","status_perkawinan":"Belum Menikah","pekerjaan":"Wiraswasta","penghasilan":"12000000.00","jumlah_tanggungan":5,"jumlah_pinjaman":"45000000.00","jangka_waktu":24,"jaminan":"BPKB Mobil","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Orang Tua","umur":32,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-16 11:05:14","updated_at":"2025-05-16 11:05:14"}
2025-05-16 04:05:14 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: jaminan BPKB mobil dengan usia kendaraan yang masih baru (2023), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":55,"max_skor":100}
2025-05-16 04:05:14 - Prediction saved with ID: 53
2025-05-16 04:05:14 - Sending response: {"id_prediksi":53,"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: jaminan BPKB mobil dengan usia kendaraan yang masih baru (2023), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-16 22:37:03 - Request received for ID: 1993
2025-05-16 22:37:03 - POST data: Array
(
    [id_nasabah] => 1993
)

2025-05-16 22:37:03 - GET data: Array
(
)

2025-05-16 22:37:03 - REQUEST data: Array
(
    [id_nasabah] => 1993
)

2025-05-16 22:37:03 - Nasabah data retrieved: {"id_nasabah":1993,"nama_nasabah":"Adul","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Wiraswasta","penghasilan":"6000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2019,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":34,"tujuan_pinjaman":"Renovasi","kelayakan":null,"created_at":"2025-05-17 05:35:26","updated_at":"2025-05-17 05:35:26"}
2025-05-16 22:37:03 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.43,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 43 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (3 orang), status kepemilikan rumah yang kurang baik.","skor":43,"max_skor":100}
2025-05-16 22:37:03 - Prediction saved with ID: 54
2025-05-16 22:37:03 - Sending response: {"id_prediksi":54,"hasil_prediksi":"Tidak Layak","probabilitas":0.43,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 43 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (3 orang), status kepemilikan rumah yang kurang baik."}
2025-05-17 00:15:04 - Request received for ID: NULL
2025-05-17 00:15:04 - POST data: Array
(
    [dataset_size] => 300
    [split_ratio] => 1
    [evaluate] => 
)

2025-05-17 00:15:04 - GET data: Array
(
)

2025-05-17 00:15:04 - REQUEST data: Array
(
    [dataset_size] => 300
    [split_ratio] => 1
    [evaluate] => 
)

2025-05-17 00:15:04 - Error: ID Nasabah tidak ditemukan
2025-05-17 00:51:34 - Request received for ID: NULL
2025-05-17 00:51:34 - POST data: Array
(
    [dataset_size] => 200
    [split_ratio] => 0
    [evaluate] => 
)

2025-05-17 00:51:34 - GET data: Array
(
)

2025-05-17 00:51:34 - REQUEST data: Array
(
    [dataset_size] => 200
    [split_ratio] => 0
    [evaluate] => 
)

2025-05-17 00:51:34 - Error: ID Nasabah tidak ditemukan
2025-05-17 00:52:06 - Request received for ID: NULL
2025-05-17 00:52:06 - POST data: Array
(
    [dataset_size] => 300
    [split_ratio] => 1
    [evaluate] => 
)

2025-05-17 00:52:06 - GET data: Array
(
)

2025-05-17 00:52:06 - REQUEST data: Array
(
    [dataset_size] => 300
    [split_ratio] => 1
    [evaluate] => 
)

2025-05-17 00:52:06 - Error: ID Nasabah tidak ditemukan
2025-05-17 02:50:14 - Request received for ID: 1994
2025-05-17 02:50:14 - POST data: Array
(
    [id_nasabah] => 1994
)

2025-05-17 02:50:14 - GET data: Array
(
)

2025-05-17 02:50:14 - REQUEST data: Array
(
    [id_nasabah] => 1994
)

2025-05-17 02:50:14 - Nasabah data retrieved: {"id_nasabah":1994,"nama_nasabah":"Samsudin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"PNS","penghasilan":"12000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":34,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 09:50:14","updated_at":"2025-05-17 09:50:14"}
2025-05-17 02:50:14 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.85,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 85 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":85,"max_skor":100}
2025-05-17 02:50:14 - Prediction saved with ID: 55
2025-05-17 02:50:14 - Sending response: {"id_prediksi":55,"hasil_prediksi":"Layak","probabilitas":0.85,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 85 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-17 03:03:37 - Request received for ID: 1995
2025-05-17 03:03:37 - POST data: Array
(
    [id_nasabah] => 1995
)

2025-05-17 03:03:37 - GET data: Array
(
)

2025-05-17 03:03:37 - REQUEST data: Array
(
    [id_nasabah] => 1995
)

2025-05-17 03:03:37 - Nasabah data retrieved: {"id_nasabah":1995,"nama_nasabah":"Nesa","jenis_kelamin":"Perempuan","status_perkawinan":"Belum Menikah","pekerjaan":"Swasta","penghasilan":"8000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":24,"jaminan":"BPKB Mobil","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Orang Tua","umur":45,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 10:03:35","updated_at":"2025-05-17 10:03:35"}
2025-05-17 03:03:37 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":55,"max_skor":100}
2025-05-17 03:03:37 - Prediction saved with ID: 56
2025-05-17 03:03:37 - Sending response: {"id_prediksi":56,"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-17 03:03:38 - Request received for ID: 1996
2025-05-17 03:03:38 - POST data: Array
(
    [id_nasabah] => 1996
)

2025-05-17 03:03:38 - GET data: Array
(
)

2025-05-17 03:03:38 - REQUEST data: Array
(
    [id_nasabah] => 1996
)

2025-05-17 03:03:38 - Nasabah data retrieved: {"id_nasabah":1996,"nama_nasabah":"Nesa","jenis_kelamin":"Perempuan","status_perkawinan":"Belum Menikah","pekerjaan":"Swasta","penghasilan":"8000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":24,"jaminan":"BPKB Mobil","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Orang Tua","umur":45,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 10:03:37","updated_at":"2025-05-17 10:03:37"}
2025-05-17 03:03:38 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":55,"max_skor":100}
2025-05-17 03:03:38 - Prediction saved with ID: 57
2025-05-17 03:03:38 - Sending response: {"id_prediksi":57,"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-17 03:25:03 - Request received for ID: NULL
2025-05-17 03:25:03 - POST data: Array
(
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 55
    [penghasilan] => 5000000
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 15
    [jaminan] => BPKB Mobil
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => 
    [predict_new] => 
)

2025-05-17 03:25:03 - GET data: Array
(
    [mode] => new
)

2025-05-17 03:25:03 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 55
    [penghasilan] => 5000000
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 15
    [jaminan] => BPKB Mobil
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => 
    [predict_new] => 
)

2025-05-17 03:25:03 - Error: ID Nasabah tidak ditemukan
2025-05-17 04:15:08 - Request received for ID: NULL
2025-05-17 04:15:08 - POST data: Array
(
    [nama_nasabah] => Dhea
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Belum Menikah
    [pekerjaan] => PNS
    [umur] => 23
    [penghasilan] => 15000000
    [jumlah_tanggungan] => 5
    [jumlah_pinjaman] => 20000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Orang Tua
    [submit] => 
)

2025-05-17 04:15:08 - GET data: Array
(
    [mode] => new
)

2025-05-17 04:15:08 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Dhea
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Belum Menikah
    [pekerjaan] => PNS
    [umur] => 23
    [penghasilan] => 15000000
    [jumlah_tanggungan] => 5
    [jumlah_pinjaman] => 20000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Orang Tua
    [submit] => 
)

2025-05-17 04:15:08 - Error: ID Nasabah tidak ditemukan
2025-05-17 04:29:01 - Request received for ID: 1998
2025-05-17 04:29:01 - POST data: Array
(
    [id_nasabah] => 1998
    [predict_leasing] => 
)

2025-05-17 04:29:01 - GET data: Array
(
)

2025-05-17 04:29:01 - REQUEST data: Array
(
    [id_nasabah] => 1998
    [predict_leasing] => 
)

2025-05-17 04:29:01 - Nasabah data retrieved: {"id_nasabah":1998,"nama_nasabah":"Dhea","jenis_kelamin":"Perempuan","status_perkawinan":"Belum Menikah","pekerjaan":"PNS","penghasilan":"15000000.00","jumlah_tanggungan":5,"jumlah_pinjaman":"20000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Orang Tua","umur":23,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 11:15:08","updated_at":"2025-05-17 11:15:08"}
2025-05-17 04:29:01 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.75,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 75 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan usia kendaraan yang masih baru (2023), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":75,"max_skor":100}
2025-05-17 04:29:02 - Prediction saved with ID: 58
2025-05-17 04:29:02 - Sending response: {"id_prediksi":58,"hasil_prediksi":"Layak","probabilitas":0.75,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 75 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan usia kendaraan yang masih baru (2023), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-17 04:31:44 - Request received for ID: NULL
2025-05-17 04:31:44 - POST data: Array
(
    [nama_nasabah] => Nur
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Wiraswasta
    [umur] => 34
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 35000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2024
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 04:31:44 - GET data: Array
(
    [mode] => new
)

2025-05-17 04:31:44 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Nur
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Wiraswasta
    [umur] => 34
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 35000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2024
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 04:31:44 - Error: ID Nasabah tidak ditemukan
2025-05-17 04:31:55 - Request received for ID: 1999
2025-05-17 04:31:55 - POST data: Array
(
    [id_nasabah] => 1999
    [predict_leasing] => 
)

2025-05-17 04:31:55 - GET data: Array
(
)

2025-05-17 04:31:55 - REQUEST data: Array
(
    [id_nasabah] => 1999
    [predict_leasing] => 
)

2025-05-17 04:31:55 - Nasabah data retrieved: {"id_nasabah":1999,"nama_nasabah":"Nur","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Wiraswasta","penghasilan":"5000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"35000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2024,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":34,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 11:31:44","updated_at":"2025-05-17 11:31:44"}
2025-05-17 04:31:55 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.5,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 50 dari 100. Faktor yang mendukung: jaminan BPKB motor dengan usia kendaraan yang masih baru (2024), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":50,"max_skor":100}
2025-05-17 04:31:56 - Prediction saved with ID: 59
2025-05-17 04:31:56 - Sending response: {"id_prediksi":59,"hasil_prediksi":"Layak","probabilitas":0.5,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 50 dari 100. Faktor yang mendukung: jaminan BPKB motor dengan usia kendaraan yang masih baru (2024), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-17 04:41:37 - Request received for ID: NULL
2025-05-17 04:41:37 - POST data: Array
(
    [nama_nasabah] => Dika
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 35
    [penghasilan] => 6000000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 35000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2021
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 04:41:37 - GET data: Array
(
    [mode] => new
)

2025-05-17 04:41:37 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Dika
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 35
    [penghasilan] => 6000000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 35000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2021
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 04:41:37 - Error: ID Nasabah tidak ditemukan
2025-05-17 04:42:01 - Request received for ID: 2000
2025-05-17 04:42:01 - POST data: Array
(
    [id_nasabah] => 2000
)

2025-05-17 04:42:01 - GET data: Array
(
)

2025-05-17 04:42:01 - REQUEST data: Array
(
    [id_nasabah] => 2000
)

2025-05-17 04:42:01 - Nasabah data retrieved: {"id_nasabah":2000,"nama_nasabah":"Dika","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"6000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"35000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":35,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 11:41:36","updated_at":"2025-05-17 11:41:36"}
2025-05-17 04:42:01 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.5,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 50 dari 100. Faktor yang mendukung: jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":50,"max_skor":100}
2025-05-17 04:42:07 - Prediction saved with ID: 60
2025-05-17 04:42:07 - Sending response: {"id_prediksi":60,"hasil_prediksi":"Layak","probabilitas":0.5,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 50 dari 100. Faktor yang mendukung: jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-17 04:56:54 - Request received for ID: NULL
2025-05-17 04:56:54 - POST data: Array
(
    [nama_nasabah] => Santi
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Wiraswasta
    [umur] => 45
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 4
    [jumlah_pinjaman] => 30000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2019
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [submit] => 
)

2025-05-17 04:56:54 - GET data: Array
(
    [mode] => new
)

2025-05-17 04:56:54 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Santi
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Wiraswasta
    [umur] => 45
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 4
    [jumlah_pinjaman] => 30000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2019
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [submit] => 
)

2025-05-17 04:56:54 - Error: ID Nasabah tidak ditemukan
2025-05-17 05:33:28 - Request received for ID: NULL
2025-05-17 05:33:28 - POST data: Array
(
    [nama_nasabah] => Suhartini
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 43
    [penghasilan] => 6000000
    [jumlah_tanggungan] => 4
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 05:33:28 - GET data: Array
(
    [mode] => new
)

2025-05-17 05:33:28 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Suhartini
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 43
    [penghasilan] => 6000000
    [jumlah_tanggungan] => 4
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 05:33:28 - Error: ID Nasabah tidak ditemukan
2025-05-17 05:33:39 - Request received for ID: 2002
2025-05-17 05:33:39 - POST data: Array
(
    [id_nasabah] => 2002
    [predict_leasing] => 
)

2025-05-17 05:33:39 - GET data: Array
(
)

2025-05-17 05:33:39 - REQUEST data: Array
(
    [id_nasabah] => 2002
    [predict_leasing] => 
)

2025-05-17 05:33:39 - Nasabah data retrieved: {"id_nasabah":2002,"nama_nasabah":"Suhartini","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"6000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"25000000.00","jangka_waktu":24,"jaminan":"BPKB Motor","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":43,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 12:33:28","updated_at":"2025-05-17 12:33:28"}
2025-05-17 05:33:39 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.4,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 40 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (4 orang), pekerjaan yang kurang stabil, jaminan BPKB motor (2023) yang nilainya kurang memadai.","skor":40,"max_skor":100}
2025-05-17 05:33:39 - Prediction saved with ID: 61
2025-05-17 05:33:39 - Sending response: {"id_prediksi":61,"hasil_prediksi":"Tidak Layak","probabilitas":0.4,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 40 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (4 orang), pekerjaan yang kurang stabil, jaminan BPKB motor (2023) yang nilainya kurang memadai."}
2025-05-17 05:50:15 - Request received for ID: NULL
2025-05-17 05:50:15 - POST data: Array
(
    [nama_nasabah] => Kevin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 35
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 05:50:15 - GET data: Array
(
    [mode] => new
)

2025-05-17 05:50:15 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Kevin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 35
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 05:50:15 - Error: ID Nasabah tidak ditemukan
2025-05-17 05:51:19 - Request received for ID: NULL
2025-05-17 05:51:19 - POST data: Array
(
    [nama_nasabah] => Kevin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 35
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 05:51:19 - GET data: Array
(
    [mode] => new
)

2025-05-17 05:51:19 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Kevin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 35
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 05:51:19 - Error: ID Nasabah tidak ditemukan
2025-05-17 05:53:07 - Request received for ID: NULL
2025-05-17 05:53:07 - POST data: Array
(
    [nama_nasabah] => Kinan
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Cerai
    [pekerjaan] => PNS
    [umur] => 33
    [penghasilan] => 8000000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2018
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 05:53:07 - GET data: Array
(
    [mode] => new
)

2025-05-17 05:53:07 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Kinan
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Cerai
    [pekerjaan] => PNS
    [umur] => 33
    [penghasilan] => 8000000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2018
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 05:53:07 - Error: ID Nasabah tidak ditemukan
2025-05-17 05:59:41 - Request received for ID: NULL
2025-05-17 05:59:41 - POST data: Array
(
    [nama_nasabah] => Kinan
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Cerai
    [pekerjaan] => PNS
    [umur] => 33
    [penghasilan] => 8000000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2018
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 05:59:41 - GET data: Array
(
    [mode] => new
)

2025-05-17 05:59:41 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Kinan
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Cerai
    [pekerjaan] => PNS
    [umur] => 33
    [penghasilan] => 8000000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2018
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 05:59:41 - Error: ID Nasabah tidak ditemukan
2025-05-17 06:01:46 - Request received for ID: 2005
2025-05-17 06:01:46 - POST data: Array
(
    [id_nasabah] => 2005
    [predict_leasing] => 
)

2025-05-17 06:01:46 - GET data: Array
(
)

2025-05-17 06:01:46 - REQUEST data: Array
(
    [id_nasabah] => 2005
    [predict_leasing] => 
)

2025-05-17 06:01:46 - Nasabah data retrieved: {"id_nasabah":2005,"nama_nasabah":"Kinan","jenis_kelamin":"Laki-laki","status_perkawinan":"Cerai","pekerjaan":"PNS","penghasilan":"8000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2018,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":33,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 12:53:06","updated_at":"2025-05-17 12:53:06"}
2025-05-17 06:01:46 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.73,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 73 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil (2018), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":73,"max_skor":100}
2025-05-17 06:01:47 - Prediction saved with ID: 63
2025-05-17 06:01:47 - Sending response: {"id_prediksi":63,"hasil_prediksi":"Layak","probabilitas":0.73,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 73 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil (2018), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-17 06:02:26 - Request received for ID: 2001
2025-05-17 06:02:26 - POST data: Array
(
    [id_nasabah] => 2001
    [predict_leasing] => 
)

2025-05-17 06:02:26 - GET data: Array
(
)

2025-05-17 06:02:26 - REQUEST data: Array
(
    [id_nasabah] => 2001
    [predict_leasing] => 
)

2025-05-17 06:02:26 - Nasabah data retrieved: {"id_nasabah":2001,"nama_nasabah":"Santi","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Wiraswasta","penghasilan":"5000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"30000000.00","jangka_waktu":24,"jaminan":"BPKB Mobil","tahun_kendaraan":2019,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":45,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 11:56:53","updated_at":"2025-05-17 11:56:53"}
2025-05-17 06:02:26 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.43,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 43 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (4 orang), status kepemilikan rumah yang kurang baik.","skor":43,"max_skor":100}
2025-05-17 06:02:26 - Prediction saved with ID: 65
2025-05-17 06:02:26 - Sending response: {"id_prediksi":65,"hasil_prediksi":"Tidak Layak","probabilitas":0.43,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 43 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (4 orang), status kepemilikan rumah yang kurang baik."}
2025-05-17 06:04:38 - Request received for ID: 1997
2025-05-17 06:04:38 - POST data: Array
(
    [id_nasabah] => 1997
    [predict_leasing] => 
)

2025-05-17 06:04:38 - GET data: Array
(
)

2025-05-17 06:04:38 - REQUEST data: Array
(
    [id_nasabah] => 1997
    [predict_leasing] => 
)

2025-05-17 06:04:38 - Nasabah data retrieved: {"id_nasabah":1997,"nama_nasabah":"Samsudin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":0,"jumlah_pinjaman":"25000000.00","jangka_waktu":15,"jaminan":"BPKB Mobil","tahun_kendaraan":null,"status_pajak":null,"kepemilikan_rumah":"Milik Sendiri","umur":55,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 10:25:03","updated_at":"2025-05-17 10:25:03"}
2025-05-17 06:04:38 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.62,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 62 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB mobil, status kepemilikan rumah yang baik.","skor":62,"max_skor":100}
2025-05-17 06:04:38 - Prediction saved with ID: 67
2025-05-17 06:04:38 - Sending response: {"id_prediksi":67,"hasil_prediksi":"Layak","probabilitas":0.62,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 62 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB mobil, status kepemilikan rumah yang baik."}
2025-05-17 06:09:19 - Request received for ID: NULL
2025-05-17 06:09:19 - POST data: Array
(
    [nama_nasabah] => Udin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Lainnya
    [umur] => 55
    [penghasilan] => 3500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 20000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2024
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 06:09:19 - GET data: Array
(
    [mode] => new
)

2025-05-17 06:09:19 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Udin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Lainnya
    [umur] => 55
    [penghasilan] => 3500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 20000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2024
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 06:09:19 - Error: ID Nasabah tidak ditemukan
2025-05-17 06:09:30 - Request received for ID: 2004
2025-05-17 06:09:30 - POST data: Array
(
    [id_nasabah] => 2004
    [predict_leasing] => 
)

2025-05-17 06:09:30 - GET data: Array
(
)

2025-05-17 06:09:30 - REQUEST data: Array
(
    [id_nasabah] => 2004
    [predict_leasing] => 
)

2025-05-17 06:09:30 - Nasabah data retrieved: {"id_nasabah":2004,"nama_nasabah":"Kevin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4500000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":35,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 12:51:18","updated_at":"2025-05-17 12:51:18"}
2025-05-17 06:09:30 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.4,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 40 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (3 orang), pekerjaan yang kurang stabil, jaminan BPKB motor (2023) yang nilainya kurang memadai.","skor":40,"max_skor":100}
2025-05-17 06:09:30 - Prediction saved with ID: 69
2025-05-17 06:09:30 - Sending response: {"id_prediksi":69,"hasil_prediksi":"Tidak Layak","probabilitas":0.4,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 40 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (3 orang), pekerjaan yang kurang stabil, jaminan BPKB motor (2023) yang nilainya kurang memadai."}
2025-05-17 12:12:17 - Request received for ID: NULL
2025-05-17 12:12:17 - POST data: Array
(
    [nama_nasabah] => Udin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 25
    [penghasilan] => 7000000
    [jumlah_tanggungan] => 4
    [jumlah_pinjaman] => 35000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 12:12:17 - GET data: Array
(
    [mode] => new
)

2025-05-17 12:12:17 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Udin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 25
    [penghasilan] => 7000000
    [jumlah_tanggungan] => 4
    [jumlah_pinjaman] => 35000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [submit] => 
)

2025-05-17 12:12:17 - Error: ID Nasabah tidak ditemukan
2025-05-17 13:47:17 - Request received for ID: 2008
2025-05-17 13:47:17 - POST data: Array
(
    [id_nasabah] => 2008
    [predict_leasing] => 
)

2025-05-17 13:47:17 - GET data: Array
(
)

2025-05-17 13:47:17 - REQUEST data: Array
(
    [id_nasabah] => 2008
    [predict_leasing] => 
)

2025-05-17 13:47:17 - Nasabah data retrieved: {"id_nasabah":2008,"nama_nasabah":"Udin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"7000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"35000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":25,"tujuan_pinjaman":"INTERNAL_PREDICTION","kelayakan":null,"created_at":"2025-05-17 19:12:16","updated_at":"2025-05-17 19:12:16"}
2025-05-17 13:47:17 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.37,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 37 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (4 orang), pekerjaan yang kurang stabil, jaminan BPKB motor (2023) yang nilainya kurang memadai.","skor":37,"max_skor":100}
2025-05-17 13:47:18 - Prediction saved with ID: 71
2025-05-17 13:47:18 - Sending response: {"id_prediksi":71,"hasil_prediksi":"Tidak Layak","probabilitas":0.37,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 37 dari 100. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang cukup banyak (4 orang), pekerjaan yang kurang stabil, jaminan BPKB motor (2023) yang nilainya kurang memadai."}
2025-05-17 13:48:40 - Request received for ID: NULL
2025-05-17 13:48:40 - POST data: Array
(
    [nama_nasabah] => Widi
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 28
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [submit] => 
)

2025-05-17 13:48:40 - GET data: Array
(
    [mode] => new
)

2025-05-17 13:48:40 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Widi
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 28
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [submit] => 
)

2025-05-17 13:48:40 - Error: ID Nasabah tidak ditemukan
2025-05-17 14:04:43 - Request received for ID: NULL
2025-05-17 14:04:43 - POST data: Array
(
    [nama_nasabah] => Adam
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 40
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => 
    [submit] => 
)

2025-05-17 14:04:43 - GET data: Array
(
    [mode] => new
)

2025-05-17 14:04:43 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Adam
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 40
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2023
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => 
    [submit] => 
)

2025-05-17 14:04:43 - Error: ID Nasabah tidak ditemukan
2025-05-17 14:09:36 - Request received for ID: NULL
2025-05-17 14:09:36 - POST data: Array
(
    [nama_nasabah] => Sule
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Wiraswasta
    [umur] => 54
    [penghasilan] => 6000000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2021
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => Renovasi
    [submit] => 
)

2025-05-17 14:09:36 - GET data: Array
(
    [mode] => new
)

2025-05-17 14:09:36 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Sule
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Wiraswasta
    [umur] => 54
    [penghasilan] => 6000000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 25000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2021
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => Renovasi
    [submit] => 
)

2025-05-17 14:09:36 - Error: ID Nasabah tidak ditemukan
2025-05-17 14:18:59 - Request received for ID: NULL
2025-05-17 14:18:59 - POST data: Array
(
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 25
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 20000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2024
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => 
    [submit] => 
)

2025-05-17 14:18:59 - GET data: Array
(
    [mode] => new
)

2025-05-17 14:18:59 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 25
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 20000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2024
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => 
    [submit] => 
)

2025-05-17 14:18:59 - Error: ID Nasabah tidak ditemukan
2025-05-17 14:22:41 - Request received for ID: NULL
2025-05-17 14:22:41 - POST data: Array
(
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 25
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 20000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2024
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => 
    [submit] => 
)

2025-05-17 14:22:41 - GET data: Array
(
    [mode] => new
)

2025-05-17 14:22:41 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 25
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 20000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2024
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => 
    [submit] => 
)

2025-05-17 14:22:41 - Error: ID Nasabah tidak ditemukan
2025-05-17 14:22:48 - Request received for ID: NULL
2025-05-17 14:22:48 - POST data: Array
(
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 25
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 20000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2024
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => 
    [submit] => 
)

2025-05-17 14:22:48 - GET data: Array
(
    [mode] => new
)

2025-05-17 14:22:48 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 25
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 20000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2024
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => 
    [submit] => 
)

2025-05-17 14:22:48 - Error: ID Nasabah tidak ditemukan
2025-05-17 14:29:30 - Request received for ID: NULL
2025-05-17 14:29:30 - POST data: Array
(
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 25
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 20000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2024
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => 
    [submit] => 
)

2025-05-17 14:29:30 - GET data: Array
(
    [mode] => new
)

2025-05-17 14:29:30 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 25
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 20000000
    [jangka_waktu] => 24
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2024
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Milik Sendiri
    [tujuan_pinjaman] => 
    [submit] => 
)

2025-05-17 14:29:30 - Error: ID Nasabah tidak ditemukan
2025-05-17 14:36:05 - Request received for ID: 2011
2025-05-17 14:36:05 - POST data: Array
(
    [id_nasabah] => 2011
    [predict_leasing] => 
)

2025-05-17 14:36:05 - GET data: Array
(
)

2025-05-17 14:36:05 - REQUEST data: Array
(
    [id_nasabah] => 2011
    [predict_leasing] => 
)

2025-05-17 14:36:05 - Nasabah data retrieved: {"id_nasabah":2011,"nama_nasabah":"Sule","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Wiraswasta","penghasilan":"6000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":24,"jaminan":"BPKB Mobil","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":54,"tujuan_pinjaman":"Renovasi","kelayakan":null,"created_at":"2025-05-17 21:09:36","updated_at":"2025-05-17 21:09:36"}
2025-05-17 14:36:05 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.57,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 57 dari 100. Faktor yang mendukung: jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":57,"max_skor":100}
2025-05-17 14:36:06 - Prediction saved with ID: 73
2025-05-17 14:36:06 - Sending response: {"id_prediksi":73,"hasil_prediksi":"Layak","probabilitas":0.57,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 57 dari 100. Faktor yang mendukung: jaminan BPKB mobil dengan usia kendaraan yang masih baru (2021), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-17 14:40:43 - Request received for ID: NULL
2025-05-17 14:40:43 - POST data: Array
(
    [nama_nasabah] => Nesa
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 14:40:43 - GET data: Array
(
    [mode] => new
)

2025-05-17 14:40:43 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Nesa
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 14:40:43 - Error: ID Nasabah tidak ditemukan
2025-05-17 14:47:39 - Request received for ID: NULL
2025-05-17 14:47:39 - POST data: Array
(
    [nama_nasabah] => Bagus
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 14:47:39 - GET data: Array
(
    [mode] => new
)

2025-05-17 14:47:39 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Bagus
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 14:47:39 - Error: ID Nasabah tidak ditemukan
2025-05-17 14:57:40 - Request received for ID: NULL
2025-05-17 14:57:40 - POST data: Array
(
    [nama_nasabah] => Bagas
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 14:57:40 - GET data: Array
(
    [mode] => new
)

2025-05-17 14:57:40 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Bagas
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Mobil
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 14:57:40 - Error: ID Nasabah tidak ditemukan
2025-05-17 14:58:10 - Request received for ID: NULL
2025-05-17 14:58:10 - POST data: Array
(
    [nama_nasabah] => Nesa
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 14:58:10 - GET data: Array
(
    [mode] => new
)

2025-05-17 14:58:10 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Nesa
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 14:58:10 - Error: ID Nasabah tidak ditemukan
2025-05-17 15:02:18 - Request received for ID: NULL
2025-05-17 15:02:18 - POST data: Array
(
    [nama_nasabah] => Nesa
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:02:18 - GET data: Array
(
    [mode] => new
)

2025-05-17 15:02:18 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Nesa
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:02:18 - Error: ID Nasabah tidak ditemukan
2025-05-17 15:02:26 - Request received for ID: NULL
2025-05-17 15:02:26 - POST data: Array
(
    [nama_nasabah] => Nesa
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:02:26 - GET data: Array
(
    [mode] => new
)

2025-05-17 15:02:26 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Nesa
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:02:26 - Error: ID Nasabah tidak ditemukan
2025-05-17 15:02:36 - Request received for ID: 2020
2025-05-17 15:02:36 - POST data: Array
(
    [id_nasabah] => 2020
    [predict_leasing] => 
)

2025-05-17 15:02:36 - GET data: Array
(
)

2025-05-17 15:02:36 - REQUEST data: Array
(
    [id_nasabah] => 2020
    [predict_leasing] => 
)

2025-05-17 15:02:36 - Nasabah data retrieved: {"id_nasabah":2020,"nama_nasabah":"Nesa","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"10000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 22:02:18","updated_at":"2025-05-17 22:02:18"}
2025-05-17 15:02:36 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif.","skor":55,"max_skor":100}
2025-05-17 15:02:36 - Prediction saved with ID: 75
2025-05-17 15:02:36 - Sending response: {"id_prediksi":75,"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif."}
2025-05-17 15:02:56 - Request received for ID: NULL
2025-05-17 15:02:56 - POST data: Array
(
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:02:56 - GET data: Array
(
    [mode] => new
)

2025-05-17 15:02:56 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:02:56 - Error: ID Nasabah tidak ditemukan
2025-05-17 15:12:39 - Request received for ID: NULL
2025-05-17 15:12:39 - POST data: Array
(
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:12:39 - GET data: Array
(
    [mode] => new
)

2025-05-17 15:12:39 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Samsudin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:12:39 - Error: ID Nasabah tidak ditemukan
2025-05-17 15:13:49 - Request received for ID: 2006
2025-05-17 15:13:49 - POST data: Array
(
    [id_nasabah] => 2006
    [predict_leasing] => 
)

2025-05-17 15:13:49 - GET data: Array
(
)

2025-05-17 15:13:49 - REQUEST data: Array
(
    [id_nasabah] => 2006
    [predict_leasing] => 
)

2025-05-17 15:13:49 - Nasabah data retrieved: {"id_nasabah":2006,"nama_nasabah":"Kinan","jenis_kelamin":"Laki-laki","status_perkawinan":"Cerai","pekerjaan":"PNS","penghasilan":"8000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2018,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":33,"tujuan_pinjaman":"","kelayakan":null,"created_at":"2025-05-17 12:59:41","updated_at":"2025-05-17 12:59:41"}
2025-05-17 15:13:49 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.73,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 73 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil (2018), status pajak kendaraan aktif, status kepemilikan rumah yang baik.","skor":73,"max_skor":100}
2025-05-17 15:13:51 - Prediction saved with ID: 77
2025-05-17 15:13:51 - Sending response: {"id_prediksi":77,"hasil_prediksi":"Layak","probabilitas":0.73,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 73 dari 100. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil (2018), status pajak kendaraan aktif, status kepemilikan rumah yang baik."}
2025-05-17 15:21:05 - Request received for ID: NULL
2025-05-17 15:21:05 - POST data: Array
(
    [nama_nasabah] => Gilang
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 4000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:21:05 - GET data: Array
(
    [mode] => new
)

2025-05-17 15:21:05 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Gilang
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 4000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:21:05 - Error: ID Nasabah tidak ditemukan
2025-05-17 15:28:08 - Request received for ID: NULL
2025-05-17 15:28:08 - POST data: Array
(
    [nama_nasabah] => Gilang
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 4000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:28:08 - GET data: Array
(
    [mode] => new
)

2025-05-17 15:28:08 - REQUEST data: Array
(
    [mode] => new
    [nama_nasabah] => Gilang
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 4000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:28:08 - Error: ID Nasabah tidak ditemukan
2025-05-17 15:28:37 - Request received for ID: 2024
2025-05-17 15:28:37 - POST data: Array
(
    [id_nasabah] => 2024
    [predict_leasing] => 
)

2025-05-17 15:28:37 - GET data: Array
(
)

2025-05-17 15:28:37 - REQUEST data: Array
(
    [id_nasabah] => 2024
    [predict_leasing] => 
)

2025-05-17 15:28:37 - Nasabah data retrieved: {"id_nasabah":2024,"nama_nasabah":"Gilang","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4000000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"10000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 22:21:05","updated_at":"2025-05-17 22:21:05","sumber_data":null}
2025-05-17 15:28:37 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif.","skor":55,"max_skor":100}
2025-05-17 15:28:37 - Prediction saved with ID: 79
2025-05-17 15:28:37 - Sending response: {"id_prediksi":79,"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif."}
2025-05-17 15:29:06 - Request received for ID: NULL
2025-05-17 15:29:06 - POST data: Array
(
    [nama_nasabah] => David
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:29:06 - GET data: Array
(
)

2025-05-17 15:29:06 - REQUEST data: Array
(
    [nama_nasabah] => David
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 5000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:29:06 - Error: ID Nasabah tidak ditemukan
2025-05-17 15:35:41 - Request received for ID: NULL
2025-05-17 15:35:41 - POST data: Array
(
    [nama_nasabah] => Desi
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 4800000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:35:41 - GET data: Array
(
)

2025-05-17 15:35:41 - REQUEST data: Array
(
    [nama_nasabah] => Desi
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 4800000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:35:41 - Error: ID Nasabah tidak ditemukan
2025-05-17 15:43:53 - Request received for ID: NULL
2025-05-17 15:43:53 - POST data: Array
(
    [nama_nasabah] => Desi
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 4800000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:43:53 - GET data: Array
(
)

2025-05-17 15:43:53 - REQUEST data: Array
(
    [nama_nasabah] => Desi
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 4800000
    [jumlah_tanggungan] => 3
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:43:53 - Error: ID Nasabah tidak ditemukan
2025-05-17 15:47:43 - Request received for ID: NULL
2025-05-17 15:47:43 - POST data: Array
(
    [nama_nasabah] => Davin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 6000000
    [jumlah_tanggungan] => 4
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:47:43 - GET data: Array
(
)

2025-05-17 15:47:43 - REQUEST data: Array
(
    [nama_nasabah] => Davin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 6000000
    [jumlah_tanggungan] => 4
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 15:47:43 - Error: ID Nasabah tidak ditemukan
2025-05-17 16:13:58 - Request received for ID: NULL
2025-05-17 16:13:58 - POST data: Array
(
    [nama_nasabah] => Santi
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 4
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2021
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 16:13:58 - GET data: Array
(
)

2025-05-17 16:13:58 - REQUEST data: Array
(
    [nama_nasabah] => Santi
    [jenis_kelamin] => Perempuan
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 4500000
    [jumlah_tanggungan] => 4
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2021
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 16:13:58 - Error: ID Nasabah tidak ditemukan
2025-05-17 16:18:14 - Request received for ID: 2023
2025-05-17 16:18:14 - POST data: Array
(
    [id_nasabah] => 2023
    [predict_leasing] => 
)

2025-05-17 16:18:14 - GET data: Array
(
)

2025-05-17 16:18:14 - REQUEST data: Array
(
    [id_nasabah] => 2023
    [predict_leasing] => 
)

2025-05-17 16:18:14 - Nasabah data retrieved: {"id_nasabah":2023,"nama_nasabah":"Samsudin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"10000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 22:12:29","updated_at":"2025-05-17 23:18:14","sumber_data":"leasing"}
2025-05-17 16:18:14 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif.","skor":55,"max_skor":100}
2025-05-17 16:18:14 - Prediction saved with ID: 81
2025-05-17 16:18:14 - Sending response: {"id_prediksi":81,"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif."}
2025-05-17 16:18:43 - Request received for ID: NULL
2025-05-17 16:18:43 - POST data: Array
(
    [nama_nasabah] => Nesa
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 6000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 16:18:43 - GET data: Array
(
)

2025-05-17 16:18:43 - REQUEST data: Array
(
    [nama_nasabah] => Nesa
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 6000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 16:18:43 - Error: ID Nasabah tidak ditemukan
2025-05-17 16:30:11 - Request received for ID: 2021
2025-05-17 16:30:11 - POST data: Array
(
    [id_nasabah] => 2021
    [predict_leasing] => 
)

2025-05-17 16:30:11 - GET data: Array
(
)

2025-05-17 16:30:11 - REQUEST data: Array
(
    [id_nasabah] => 2021
    [predict_leasing] => 
)

2025-05-17 16:30:11 - Nasabah data retrieved: {"id_nasabah":2021,"nama_nasabah":"Nesa","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"10000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 22:02:26","updated_at":"2025-05-17 23:30:11","sumber_data":"leasing"}
2025-05-17 16:30:11 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif.","skor":55,"max_skor":100}
2025-05-17 16:30:11 - Prediction saved with ID: 83
2025-05-17 16:30:11 - Sending response: {"id_prediksi":83,"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif."}
2025-05-17 16:30:37 - Request received for ID: NULL
2025-05-17 16:30:37 - POST data: Array
(
    [nama_nasabah] => Udin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 3000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 16:30:37 - GET data: Array
(
)

2025-05-17 16:30:37 - REQUEST data: Array
(
    [nama_nasabah] => Udin
    [jenis_kelamin] => Laki-laki
    [status_perkawinan] => Menikah
    [pekerjaan] => Swasta
    [umur] => 30
    [penghasilan] => 3000000
    [jumlah_tanggungan] => 1
    [jumlah_pinjaman] => 10000000
    [jangka_waktu] => 12
    [jaminan] => BPKB Motor
    [tahun_kendaraan] => 2020
    [status_pajak] => Aktif
    [kepemilikan_rumah] => Kontrak
    [tujuan_pinjaman] => Kebutuhan Pribadi
    [submit] => 
)

2025-05-17 16:30:37 - Error: ID Nasabah tidak ditemukan
2025-05-17 17:07:21 - Request received for ID: NULL
2025-05-17 17:07:21 - POST data: Array
(
)

2025-05-17 17:07:21 - GET data: Array
(
    [id_nasabah] => 2036
    [id_prediksi] => 2
)

2025-05-17 17:07:21 - REQUEST data: Array
(
    [id_nasabah] => 2036
    [id_prediksi] => 2
)

2025-05-17 17:07:21 - Error: ID Nasabah tidak ditemukan
2025-05-17 17:09:10 - Request received for ID: NULL
2025-05-17 17:09:10 - POST data: Array
(
)

2025-05-17 17:09:10 - GET data: Array
(
    [id_nasabah] => 2036
    [id_prediksi] => 2
)

2025-05-17 17:09:10 - REQUEST data: Array
(
    [id_nasabah] => 2036
    [id_prediksi] => 2
)

2025-05-17 17:09:10 - Error: ID Nasabah tidak ditemukan
2025-05-17 17:14:03 - Request received for ID: NULL
2025-05-17 17:14:03 - POST data: Array
(
)

2025-05-17 17:14:03 - GET data: Array
(
    [id_nasabah] => 2037
    [id_prediksi] => 3
)

2025-05-17 17:14:03 - REQUEST data: Array
(
    [id_nasabah] => 2037
    [id_prediksi] => 3
)

2025-05-17 17:14:03 - Error: ID Nasabah tidak ditemukan
2025-05-17 17:18:04 - Request received for ID: 2037
2025-05-17 17:18:04 - POST data: Array
(
)

2025-05-17 17:18:04 - GET data: Array
(
    [id_nasabah] => 2037
    [id_prediksi] => 3
)

2025-05-17 17:18:04 - REQUEST data: Array
(
    [id_nasabah] => 2037
    [id_prediksi] => 3
)

2025-05-17 17:18:04 - Nasabah data retrieved: {"id_nasabah":2037,"nama_nasabah":"Dhea","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"6000000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"10000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":"Layak","created_at":"2025-05-18 00:14:03","updated_at":"2025-05-18 00:14:03","sumber_data":null}
2025-05-17 17:18:05 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif.","skor":55,"max_skor":100}
2025-05-17 17:18:08 - Prediction saved with ID: 85
2025-05-17 17:18:08 - Sending response: {"id_prediksi":85,"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif."}
2025-05-17 17:19:07 - Request received for ID: 2037
2025-05-17 17:19:07 - POST data: Array
(
)

2025-05-17 17:19:07 - GET data: Array
(
    [id_nasabah] => 2037
    [id_prediksi] => 3
)

2025-05-17 17:19:07 - REQUEST data: Array
(
    [id_nasabah] => 2037
    [id_prediksi] => 3
)

2025-05-17 17:19:07 - Nasabah data retrieved: {"id_nasabah":2037,"nama_nasabah":"Dhea","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"6000000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"10000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":"Layak","created_at":"2025-05-18 00:14:03","updated_at":"2025-05-18 00:14:03","sumber_data":null}
2025-05-17 17:19:08 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif.","skor":55,"max_skor":100}
2025-05-17 17:19:08 - Prediction saved with ID: 87
2025-05-17 17:19:08 - Sending response: {"id_prediksi":87,"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif."}
2025-05-17 17:19:19 - Request received for ID: 2019
2025-05-17 17:19:19 - POST data: Array
(
    [id_nasabah] => 2019
    [predict_leasing] => 
)

2025-05-17 17:19:19 - GET data: Array
(
)

2025-05-17 17:19:19 - REQUEST data: Array
(
    [id_nasabah] => 2019
    [predict_leasing] => 
)

2025-05-17 17:19:19 - Nasabah data retrieved: {"id_nasabah":2019,"nama_nasabah":"Nesa","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"10000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 21:58:10","updated_at":"2025-05-18 00:19:18","sumber_data":"leasing"}
2025-05-17 17:19:19 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif.","skor":55,"max_skor":100}
2025-05-17 17:19:19 - Prediction saved with ID: 89
2025-05-17 17:19:19 - Sending response: {"id_prediksi":89,"hasil_prediksi":"Layak","probabilitas":0.55,"keterangan":"Nasabah diprediksi layak menerima kredit dengan skor 55 dari 100. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor (2020), status pajak kendaraan aktif."}
2025-05-17 17:34:38 - Request received for ID: 2040
2025-05-17 17:34:38 - POST data: Array
(
)

2025-05-17 17:34:38 - GET data: Array
(
    [id_nasabah] => 2040
    [id_prediksi] => 6
)

2025-05-17 17:34:38 - REQUEST data: Array
(
    [id_nasabah] => 2040
    [id_prediksi] => 6
)

2025-05-17 17:34:38 - Nasabah data retrieved: {"id_nasabah":2040,"nama_nasabah":"Dedi","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"6000000.00","jumlah_tanggungan":5,"jumlah_pinjaman":"50000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Tidak Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":"Tidak Layak","created_at":"2025-05-18 00:34:32","updated_at":"2025-05-18 00:34:34","sumber_data":null}
2025-05-17 17:34:38 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.23147521650098238,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 38 dari 100. Faktor yang perlu diperhatikan: pekerjaan yang kurang stabil, jaminan BPKB motor (2020) yang nilainya kurang memadai, status pajak kendaraan tidak aktif, status kepemilikan rumah yang kurang baik.","skor":38,"max_skor":100}
2025-05-17 17:34:43 - Prediction saved with ID: 91
2025-05-17 17:34:43 - Sending response: {"id_prediksi":91,"hasil_prediksi":"Tidak Layak","probabilitas":0.23147521650098238,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan skor 38 dari 100. Faktor yang perlu diperhatikan: pekerjaan yang kurang stabil, jaminan BPKB motor (2020) yang nilainya kurang memadai, status pajak kendaraan tidak aktif, status kepemilikan rumah yang kurang baik."}
2025-05-17 22:28:52 - Request received for ID: 2040
2025-05-17 22:28:52 - POST data: Array
(
)

2025-05-17 22:28:52 - GET data: Array
(
    [id_nasabah] => 2040
    [id_prediksi] => 6
)

2025-05-17 22:28:52 - REQUEST data: Array
(
    [id_nasabah] => 2040
    [id_prediksi] => 6
)

2025-05-17 22:28:52 - Nasabah data retrieved: {"id_nasabah":2040,"nama_nasabah":"Dedi","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"6000000.00","jumlah_tanggungan":5,"jumlah_pinjaman":"50000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Tidak Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":"Tidak Layak","created_at":"2025-05-18 00:34:32","updated_at":"2025-05-18 00:34:34","sumber_data":null}
2025-05-17 22:28:52 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.23147521650098238,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 76.85%. Faktor yang perlu diperhatikan: jumlah tanggungan yang cukup banyak (5 orang), penghasilan per anggota keluarga yang rendah (Rp 1.000.000), pekerjaan yang kurang stabil, jaminan BPKB motor (2020) yang nilainya kurang memadai, status pajak kendaraan tidak aktif, status kepemilikan rumah yang kurang baik.","skor":38,"max_skor":100}
2025-05-17 22:28:54 - Prediction saved with ID: 93
2025-05-17 22:28:54 - Sending response: {"id_prediksi":93,"hasil_prediksi":"Tidak Layak","probabilitas":0.23147521650098238,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 76.85%. Faktor yang perlu diperhatikan: jumlah tanggungan yang cukup banyak (5 orang), penghasilan per anggota keluarga yang rendah (Rp 1.000.000), pekerjaan yang kurang stabil, jaminan BPKB motor (2020) yang nilainya kurang memadai, status pajak kendaraan tidak aktif, status kepemilikan rumah yang kurang baik."}
2025-05-18 00:00:29 - Request received for ID: 2045
2025-05-18 00:00:29 - POST data: Array
(
)

2025-05-18 00:00:29 - GET data: Array
(
    [id_nasabah] => 2045
    [id_prediksi] => 11
)

2025-05-18 00:00:29 - REQUEST data: Array
(
    [id_nasabah] => 2045
    [id_prediksi] => 11
)

2025-05-18 00:00:29 - Nasabah data retrieved: {"id_nasabah":2045,"nama_nasabah":"Santi","jenis_kelamin":"Perempuan","status_perkawinan":"Cerai","pekerjaan":"Swasta","penghasilan":"4000000.00","jumlah_tanggungan":5,"jumlah_pinjaman":"20000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Tidak Aktif","kepemilikan_rumah":"Kontrak","umur":34,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":"Tidak Layak","created_at":"2025-05-18 07:00:26","updated_at":"2025-05-18 07:00:26","sumber_data":null}
2025-05-18 00:00:29 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.27,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 73.00%. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi (2.50), penghasilan per anggota keluarga yang rendah (Rp 666.667), jumlah tanggungan yang cukup banyak (5 orang) dengan penghasilan yang kurang memadai, pekerjaan yang kurang stabil, jaminan BPKB motor (tahun 2020) yang nilainya kurang memadai dibanding jumlah pinjaman, status pajak kendaraan tidak aktif, status kepemilikan rumah yang kurang baik.","skor":27,"max_skor":100}
2025-05-18 00:00:29 - Prediction saved with ID: 95
2025-05-18 00:00:29 - Sending response: {"id_prediksi":95,"hasil_prediksi":"Tidak Layak","probabilitas":0.27,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 73.00%. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi (2.50), penghasilan per anggota keluarga yang rendah (Rp 666.667), jumlah tanggungan yang cukup banyak (5 orang) dengan penghasilan yang kurang memadai, pekerjaan yang kurang stabil, jaminan BPKB motor (tahun 2020) yang nilainya kurang memadai dibanding jumlah pinjaman, status pajak kendaraan tidak aktif, status kepemilikan rumah yang kurang baik."}
2025-05-18 00:01:20 - Request received for ID: 2022
2025-05-18 00:01:20 - POST data: Array
(
    [id_nasabah] => 2022
    [predict_leasing] => 
)

2025-05-18 00:01:20 - GET data: Array
(
)

2025-05-18 00:01:20 - REQUEST data: Array
(
    [id_nasabah] => 2022
    [predict_leasing] => 
)

2025-05-18 00:01:20 - Nasabah data retrieved: {"id_nasabah":2022,"nama_nasabah":"Samsudin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"10000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 22:02:56","updated_at":"2025-05-18 07:01:20","sumber_data":"leasing"}
2025-05-18 00:01:20 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.53,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 53.00%. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik (0.33), penghasilan per kapita yang cukup (Rp 2.500.000), jumlah tanggungan yang sedikit (1 orang), jaminan BPKB motor dengan usia kendaraan yang cukup baru (tahun 2020), status pajak kendaraan aktif.","skor":53,"max_skor":100}
2025-05-18 00:01:20 - Prediction saved with ID: 97
2025-05-18 00:01:20 - Sending response: {"id_prediksi":97,"hasil_prediksi":"Layak","probabilitas":0.53,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 53.00%. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik (0.33), penghasilan per kapita yang cukup (Rp 2.500.000), jumlah tanggungan yang sedikit (1 orang), jaminan BPKB motor dengan usia kendaraan yang cukup baru (tahun 2020), status pajak kendaraan aktif."}
2025-05-18 02:31:20 - Request received for ID: 2053
2025-05-18 02:31:20 - Nasabah data retrieved: {"id_nasabah":2053,"nama_nasabah":"Jojo","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"PNS","penghasilan":"6000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":32,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-18 09:31:18","updated_at":"2025-05-18 09:31:18","sumber_data":null}
2025-05-18 02:31:23 - Error during prediction: GAGAL TERHUBUNG KE SERVER MODEL PYTHON!

Pastikan server model Python sudah dijalankan dengan menjalankan file start_model_server.bat terlebih dahulu.

Cara menjalankan server model Python:
1. Buka Command Prompt atau PowerShell
2. Navigasi ke direktori ini
3. Jalankan perintah: .\start_model_server.bat
4. Tunggu hingga server model Python berjalan (akan muncul pesan "Running on http://0.0.0.0:5000")
5. Biarkan jendela Command Prompt/PowerShell tetap terbuka

Error detail: Failed to connect to localhost port 5000: Connection refused
2025-05-18 02:32:20 - Request received for ID: 2054
2025-05-18 02:32:20 - Nasabah data retrieved: {"id_nasabah":2054,"nama_nasabah":"Jojo","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"PNS","penghasilan":"6000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":32,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-18 09:32:20","updated_at":"2025-05-18 09:32:20","sumber_data":null}
2025-05-18 02:32:30 - Error during prediction: GAGAL TERHUBUNG KE SERVER MODEL PYTHON!

Pastikan server model Python sudah dijalankan dengan menjalankan file start_model_server.bat terlebih dahulu.

Cara menjalankan server model Python:
1. Buka Command Prompt atau PowerShell
2. Navigasi ke direktori ini
3. Jalankan perintah: .\start_model_server.bat
4. Tunggu hingga server model Python berjalan (akan muncul pesan "Running on http://0.0.0.0:5000")
5. Biarkan jendela Command Prompt/PowerShell tetap terbuka

Error detail: Operation timed out after 10007 milliseconds with 0 bytes received
2025-05-18 02:43:24 - Request received for ID: 2055
2025-05-18 02:43:25 - Nasabah data retrieved: {"id_nasabah":2055,"nama_nasabah":"Jojo","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"PNS","penghasilan":"6000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":32,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-18 09:43:24","updated_at":"2025-05-18 09:43:24","sumber_data":null}
2025-05-18 02:43:28 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999999858801316,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: rasio pinjaman terhadap penghasilan yang baik, pekerjaan yang stabil, jaminan yang memadai, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-18 02:43:28 - Prediction saved with ID: 101
2025-05-18 02:43:28 - Sending response: {"id_prediksi":101,"hasil_prediksi":"Layak","probabilitas":0.9999999858801316,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: rasio pinjaman terhadap penghasilan yang baik, pekerjaan yang stabil, jaminan yang memadai, status kepemilikan rumah yang baik."}
2025-05-18 02:43:37 - Request received for ID: 2056
2025-05-18 02:43:37 - Nasabah data retrieved: {"id_nasabah":2056,"nama_nasabah":"Jojo","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"PNS","penghasilan":"6000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":32,"tujuan_pinjaman":null,"kelayakan":null,"created_at":"2025-05-18 09:43:36","updated_at":"2025-05-18 09:43:36","sumber_data":null}
2025-05-18 02:43:37 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999999858801316,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: rasio pinjaman terhadap penghasilan yang baik, pekerjaan yang stabil, jaminan yang memadai, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-18 02:43:37 - Prediction saved with ID: 103
2025-05-18 02:43:37 - Sending response: {"id_prediksi":103,"hasil_prediksi":"Layak","probabilitas":0.9999999858801316,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: rasio pinjaman terhadap penghasilan yang baik, pekerjaan yang stabil, jaminan yang memadai, status kepemilikan rumah yang baik."}
2025-05-18 05:30:24 - Request received for ID: 2063
2025-05-18 05:30:24 - Nasabah data retrieved: {"id_nasabah":2063,"nama_nasabah":"Nesa","jenis_kelamin":"Perempuan","status_perkawinan":"Belum Menikah","pekerjaan":"Swasta","penghasilan":"3000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-18 12:17:53","updated_at":"2025-05-18 12:30:24","sumber_data":"leasing"}
2025-05-18 05:30:25 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.9103988660140296,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 8.96%. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang banyak (4 orang), pekerjaan yang kurang stabil, status kepemilikan rumah yang kurang baik.","skor":0,"max_skor":100}
2025-05-18 05:30:25 - Prediction saved with ID: 113
2025-05-18 05:30:25 - Sending response: {"id_prediksi":113,"hasil_prediksi":"Tidak Layak","probabilitas":0.9103988660140296,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 8.96%. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang banyak (4 orang), pekerjaan yang kurang stabil, status kepemilikan rumah yang kurang baik."}
2025-05-18 05:53:59 - Request received for ID: 2063
2025-05-18 05:53:59 - Nasabah data retrieved: {"id_nasabah":2063,"nama_nasabah":"Nesa","jenis_kelamin":"Perempuan","status_perkawinan":"Belum Menikah","pekerjaan":"Swasta","penghasilan":"3000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":"Tidak Layak","created_at":"2025-05-18 12:17:53","updated_at":"2025-05-18 12:30:25","sumber_data":"leasing"}
2025-05-18 05:54:03 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.9103988660140296,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 8.96%. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang banyak (4 orang), pekerjaan yang kurang stabil, status kepemilikan rumah yang kurang baik.","skor":0,"max_skor":100}
2025-05-18 05:54:03 - Prediction saved with ID: 115
2025-05-18 05:54:03 - Sending response: {"id_prediksi":115,"hasil_prediksi":"Tidak Layak","probabilitas":0.9103988660140296,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 8.96%. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang banyak (4 orang), pekerjaan yang kurang stabil, status kepemilikan rumah yang kurang baik."}
2025-05-18 06:30:35 - Request received for ID: 2018
2025-05-18 06:30:35 - Nasabah data retrieved: {"id_nasabah":2018,"nama_nasabah":"Bagas","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"10000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 21:57:40","updated_at":"2025-05-18 13:30:35","sumber_data":"leasing"}
2025-05-18 06:30:38 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9985588517122488,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.86%. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB mobil dengan kondisi baik dan pajak aktif.","skor":0,"max_skor":100}
2025-05-18 06:30:38 - Prediction saved with ID: 118
2025-05-18 06:30:38 - Sending response: {"id_prediksi":118,"hasil_prediksi":"Layak","probabilitas":0.9985588517122488,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.86%. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB mobil dengan kondisi baik dan pajak aktif."}
2025-05-18 06:34:21 - Request received for ID: 2068
2025-05-18 06:34:21 - Nasabah data retrieved: {"id_nasabah":2068,"nama_nasabah":"Ria","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"PNS","penghasilan":"8000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"26000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2019,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":46,"tujuan_pinjaman":"Renovasi","kelayakan":null,"created_at":"2025-05-18 13:33:34","updated_at":"2025-05-18 13:34:21","sumber_data":"leasing"}
2025-05-18 06:34:22 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999999998500018,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-18 06:34:22 - Prediction saved with ID: 121
2025-05-18 06:34:22 - Sending response: {"id_prediksi":121,"hasil_prediksi":"Layak","probabilitas":0.9999999998500018,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: pekerjaan yang stabil, jaminan BPKB mobil dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-18 06:36:22 - Request received for ID: 2057
2025-05-18 06:36:22 - Nasabah data retrieved: {"id_nasabah":2057,"nama_nasabah":"Samsudin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4000000.00","jumlah_tanggungan":5,"jumlah_pinjaman":"20000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-18 11:45:16","updated_at":"2025-05-18 13:36:22","sumber_data":"leasing"}
2025-05-18 06:36:22 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9725199518631822,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 97.25%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif.","skor":0,"max_skor":100}
2025-05-18 06:36:22 - Prediction saved with ID: 125
2025-05-18 06:36:22 - Sending response: {"id_prediksi":125,"hasil_prediksi":"Layak","probabilitas":0.9725199518631822,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 97.25%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif."}
2025-05-18 07:03:28 - Request received for ID: NULL
2025-05-18 07:03:28 - Error: ID Nasabah tidak ditemukan
2025-05-18 07:03:49 - Request received for ID: NULL
2025-05-18 07:03:49 - Error: ID Nasabah tidak ditemukan
2025-05-18 07:06:17 - Request received for ID: NULL
2025-05-18 07:06:17 - Error: ID Nasabah tidak ditemukan
2025-05-18 07:16:10 - Request received for ID: NULL
2025-05-18 07:16:10 - Error: ID Nasabah tidak ditemukan
2025-05-18 07:16:19 - Request received for ID: NULL
2025-05-18 07:16:19 - Error: ID Nasabah tidak ditemukan
2025-05-18 07:16:38 - Request received for ID: NULL
2025-05-18 07:16:38 - Error: ID Nasabah tidak ditemukan
2025-05-18 07:20:56 - Request received for ID: NULL
2025-05-18 07:20:56 - Error: ID Nasabah tidak ditemukan
2025-05-18 07:22:36 - Request received for ID: NULL
2025-05-18 07:22:36 - Error: ID Nasabah tidak ditemukan
2025-05-18 09:51:07 - Request received for ID: 2010
2025-05-18 09:51:07 - Nasabah data retrieved: {"id_nasabah":2010,"nama_nasabah":"Adam","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":40,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 21:04:43","updated_at":"2025-05-18 16:51:07","sumber_data":"leasing"}
2025-05-18 09:51:09 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999996071261423,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-18 09:51:09 - Prediction saved with ID: 128
2025-05-18 09:51:09 - Sending response: {"id_prediksi":128,"hasil_prediksi":"Layak","probabilitas":0.9999996071261423,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-18 09:51:53 - Request received for ID: 2062
2025-05-18 09:51:53 - Nasabah data retrieved: {"id_nasabah":2062,"nama_nasabah":"Nesa","jenis_kelamin":"Perempuan","status_perkawinan":"Belum Menikah","pekerjaan":"Swasta","penghasilan":"3000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-18 12:01:04","updated_at":"2025-05-18 16:51:53","sumber_data":"leasing"}
2025-05-18 09:51:53 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.9103988660140296,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 8.96%. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang banyak (4 orang), pekerjaan yang kurang stabil, status kepemilikan rumah yang kurang baik.","skor":0,"max_skor":100}
2025-05-18 09:51:53 - Prediction saved with ID: 131
2025-05-18 09:51:53 - Sending response: {"id_prediksi":131,"hasil_prediksi":"Tidak Layak","probabilitas":0.9103988660140296,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 8.96%. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang banyak (4 orang), pekerjaan yang kurang stabil, status kepemilikan rumah yang kurang baik."}
2025-05-18 09:52:06 - Request received for ID: 2017
2025-05-18 09:52:06 - Nasabah data retrieved: {"id_nasabah":2017,"nama_nasabah":"Bagus","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"10000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 21:47:39","updated_at":"2025-05-18 16:52:06","sumber_data":"leasing"}
2025-05-18 09:52:06 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9985588517122488,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.86%. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor dengan pajak aktif.","skor":0,"max_skor":100}
2025-05-18 09:52:06 - Prediction saved with ID: 134
2025-05-18 09:52:06 - Sending response: {"id_prediksi":134,"hasil_prediksi":"Layak","probabilitas":0.9985588517122488,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.86%. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor dengan pajak aktif."}
2025-05-18 09:52:20 - Request received for ID: 2016
2025-05-18 09:52:20 - Nasabah data retrieved: {"id_nasabah":2016,"nama_nasabah":"Nesa","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"10000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":30,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 21:40:43","updated_at":"2025-05-18 16:52:20","sumber_data":"leasing"}
2025-05-18 09:52:20 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9991734391371964,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.92%. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor dengan pajak aktif.","skor":0,"max_skor":100}
2025-05-18 09:52:20 - Prediction saved with ID: 137
2025-05-18 09:52:20 - Sending response: {"id_prediksi":137,"hasil_prediksi":"Layak","probabilitas":0.9991734391371964,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.92%. Faktor yang mendukung: rasio pinjaman terhadap penghasilan per kapita yang baik, jaminan BPKB motor dengan pajak aktif."}
2025-05-18 09:52:33 - Request received for ID: 2015
2025-05-18 09:52:33 - Nasabah data retrieved: {"id_nasabah":2015,"nama_nasabah":"Samsudin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4500000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"20000000.00","jangka_waktu":24,"jaminan":"BPKB Motor","tahun_kendaraan":2024,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":25,"tujuan_pinjaman":"","kelayakan":null,"created_at":"2025-05-17 21:29:30","updated_at":"2025-05-18 16:52:33","sumber_data":"leasing"}
2025-05-18 09:52:34 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999125092978515,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.99%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-18 09:52:34 - Prediction saved with ID: 140
2025-05-18 09:52:34 - Sending response: {"id_prediksi":140,"hasil_prediksi":"Layak","probabilitas":0.9999125092978515,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.99%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-18 09:52:43 - Request received for ID: 2014
2025-05-18 09:52:43 - Nasabah data retrieved: {"id_nasabah":2014,"nama_nasabah":"Samsudin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4500000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"20000000.00","jangka_waktu":24,"jaminan":"BPKB Motor","tahun_kendaraan":2024,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":25,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 21:22:48","updated_at":"2025-05-18 16:52:43","sumber_data":"leasing"}
2025-05-18 09:52:43 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999125092978515,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.99%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-18 09:52:43 - Prediction saved with ID: 143
2025-05-18 09:52:43 - Sending response: {"id_prediksi":143,"hasil_prediksi":"Layak","probabilitas":0.9999125092978515,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.99%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-18 09:52:51 - Request received for ID: 2013
2025-05-18 09:52:51 - Nasabah data retrieved: {"id_nasabah":2013,"nama_nasabah":"Samsudin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4500000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"20000000.00","jangka_waktu":24,"jaminan":"BPKB Motor","tahun_kendaraan":2024,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":25,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 21:22:40","updated_at":"2025-05-18 16:52:51","sumber_data":"leasing"}
2025-05-18 09:52:52 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999125092978515,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.99%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-18 09:52:52 - Prediction saved with ID: 146
2025-05-18 09:52:52 - Sending response: {"id_prediksi":146,"hasil_prediksi":"Layak","probabilitas":0.9999125092978515,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.99%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-18 09:53:01 - Request received for ID: 2012
2025-05-18 09:53:01 - Nasabah data retrieved: {"id_nasabah":2012,"nama_nasabah":"Samsudin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4500000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"20000000.00","jangka_waktu":24,"jaminan":"BPKB Motor","tahun_kendaraan":2024,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":25,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-17 21:18:59","updated_at":"2025-05-18 16:53:01","sumber_data":"leasing"}
2025-05-18 09:53:01 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999125092978515,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.99%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-18 09:53:01 - Prediction saved with ID: 149
2025-05-18 09:53:01 - Sending response: {"id_prediksi":149,"hasil_prediksi":"Layak","probabilitas":0.9999125092978515,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.99%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-18 09:53:11 - Request received for ID: 2007
2025-05-18 09:53:11 - Nasabah data retrieved: {"id_nasabah":2007,"nama_nasabah":"Udin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Lainnya","penghasilan":"3500000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"20000000.00","jangka_waktu":24,"jaminan":"BPKB Motor","tahun_kendaraan":2024,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":55,"tujuan_pinjaman":"","kelayakan":null,"created_at":"2025-05-17 13:09:19","updated_at":"2025-05-18 16:53:11","sumber_data":"leasing"}
2025-05-18 09:53:11 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9992005349519829,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.92%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-18 09:53:11 - Prediction saved with ID: 152
2025-05-18 09:53:11 - Sending response: {"id_prediksi":152,"hasil_prediksi":"Layak","probabilitas":0.9992005349519829,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.92%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-18 12:41:16 - Request received for ID: 2070
2025-05-18 12:41:16 - Nasabah data retrieved: {"id_nasabah":2070,"nama_nasabah":"Siti Nurjanah","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2022,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":46,"tujuan_pinjaman":"Modal Usaha","kelayakan":null,"created_at":"2025-05-18 19:29:28","updated_at":"2025-05-18 19:41:16","sumber_data":"leasing"}
2025-05-18 12:41:18 - Error during prediction: GAGAL TERHUBUNG KE SERVER MODEL PYTHON!

Pastikan server model Python sudah dijalankan dengan menjalankan file start_model_server.bat terlebih dahulu.

Cara menjalankan server model Python:
1. Buka Command Prompt atau PowerShell
2. Navigasi ke direktori ini
3. Jalankan perintah: .\start_model_server.bat
4. Tunggu hingga server model Python berjalan (akan muncul pesan "Running on http://0.0.0.0:5000")
5. Biarkan jendela Command Prompt/PowerShell tetap terbuka

Error detail: Failed to connect to localhost port 5000: Connection refused
2025-05-18 12:44:41 - Request received for ID: 2070
2025-05-18 12:44:41 - Nasabah data retrieved: {"id_nasabah":2070,"nama_nasabah":"Siti Nurjanah","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2022,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":46,"tujuan_pinjaman":"Modal Usaha","kelayakan":null,"created_at":"2025-05-18 19:29:28","updated_at":"2025-05-18 19:41:16","sumber_data":"leasing"}
2025-05-18 12:44:51 - Error during prediction: GAGAL TERHUBUNG KE SERVER MODEL PYTHON!

Pastikan server model Python sudah dijalankan dengan menjalankan file start_model_server.bat terlebih dahulu.

Cara menjalankan server model Python:
1. Buka Command Prompt atau PowerShell
2. Navigasi ke direktori ini
3. Jalankan perintah: .\start_model_server.bat
4. Tunggu hingga server model Python berjalan (akan muncul pesan "Running on http://0.0.0.0:5000")
5. Biarkan jendela Command Prompt/PowerShell tetap terbuka

Error detail: Operation timed out after 10005 milliseconds with 0 bytes received
2025-05-18 12:45:19 - Request received for ID: 2070
2025-05-18 12:45:19 - Nasabah data retrieved: {"id_nasabah":2070,"nama_nasabah":"Siti Nurjanah","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2022,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":46,"tujuan_pinjaman":"Modal Usaha","kelayakan":"Layak","created_at":"2025-05-18 19:29:28","updated_at":"2025-05-18 19:45:10","sumber_data":"leasing"}
2025-05-18 12:45:20 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999969451405112,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-18 12:45:20 - Prediction saved with ID: 156
2025-05-18 12:45:20 - Sending response: {"id_prediksi":156,"hasil_prediksi":"Layak","probabilitas":0.9999969451405112,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-18 14:26:28 - Request received for ID: 2071
2025-05-18 14:26:28 - Nasabah data retrieved: {"id_nasabah":2071,"nama_nasabah":"Sarah","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":34,"tujuan_pinjaman":"Modal Usaha","kelayakan":null,"created_at":"2025-05-18 20:39:38","updated_at":"2025-05-18 21:26:28","sumber_data":"leasing"}
2025-05-18 14:26:31 - Error during prediction: GAGAL TERHUBUNG KE SERVER MODEL PYTHON!

Pastikan server model Python sudah dijalankan dengan menjalankan file start_model_server.bat terlebih dahulu.

Cara menjalankan server model Python:
1. Buka Command Prompt atau PowerShell
2. Navigasi ke direktori ini
3. Jalankan perintah: .\start_model_server.bat
4. Tunggu hingga server model Python berjalan (akan muncul pesan "Running on http://0.0.0.0:5000")
5. Biarkan jendela Command Prompt/PowerShell tetap terbuka

Error detail: Failed to connect to localhost port 5000: Connection refused
2025-05-18 14:27:09 - Request received for ID: 2071
2025-05-18 14:27:09 - Nasabah data retrieved: {"id_nasabah":2071,"nama_nasabah":"Sarah","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":34,"tujuan_pinjaman":"Modal Usaha","kelayakan":null,"created_at":"2025-05-18 20:39:38","updated_at":"2025-05-18 21:26:28","sumber_data":"leasing"}
2025-05-18 14:27:19 - Error during prediction: GAGAL TERHUBUNG KE SERVER MODEL PYTHON!

Pastikan server model Python sudah dijalankan dengan menjalankan file start_model_server.bat terlebih dahulu.

Cara menjalankan server model Python:
1. Buka Command Prompt atau PowerShell
2. Navigasi ke direktori ini
3. Jalankan perintah: .\start_model_server.bat
4. Tunggu hingga server model Python berjalan (akan muncul pesan "Running on http://0.0.0.0:5000")
5. Biarkan jendela Command Prompt/PowerShell tetap terbuka

Error detail: Operation timed out after 10004 milliseconds with 0 bytes received
2025-05-18 14:27:28 - Request received for ID: 2071
2025-05-18 14:27:28 - Nasabah data retrieved: {"id_nasabah":2071,"nama_nasabah":"Sarah","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":34,"tujuan_pinjaman":"Modal Usaha","kelayakan":"Layak","created_at":"2025-05-18 20:39:38","updated_at":"2025-05-18 21:27:20","sumber_data":"leasing"}
2025-05-18 14:27:29 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999728771393812,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-18 14:27:29 - Prediction saved with ID: 160
2025-05-18 14:27:29 - Sending response: {"id_prediksi":160,"hasil_prediksi":"Layak","probabilitas":0.9999728771393812,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-20 05:35:27 - Request received for ID: 2086
2025-05-20 05:35:27 - Nasabah data retrieved: {"id_nasabah":2086,"nama_nasabah":"Fatin","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Wiraswasta","penghasilan":"3500000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"30000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":37,"tujuan_pinjaman":"Modal Usaha","kelayakan":null,"created_at":"2025-05-20 12:33:22","updated_at":"2025-05-20 12:35:27","sumber_data":"leasing"}
2025-05-20 05:35:29 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9969247355455226,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.69%. Faktor yang mendukung: jaminan BPKB mobil dengan kondisi baik dan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-20 05:35:29 - Prediction saved with ID: 177
2025-05-20 05:35:29 - Sending response: {"id_prediksi":177,"hasil_prediksi":"Layak","probabilitas":0.9969247355455226,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.69%. Faktor yang mendukung: jaminan BPKB mobil dengan kondisi baik dan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-20 08:25:24 - Request received for ID: 2090
2025-05-20 08:25:25 - Nasabah data retrieved: {"id_nasabah":2090,"nama_nasabah":"Susi","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":37,"tujuan_pinjaman":"Modal Usaha","kelayakan":null,"created_at":"2025-05-20 15:25:12","updated_at":"2025-05-20 15:25:24","sumber_data":"leasing"}
2025-05-20 08:25:25 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9958546541623811,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.59%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif.","skor":0,"max_skor":100}
2025-05-20 08:25:25 - Prediction saved with ID: 183
2025-05-20 08:25:25 - Sending response: {"id_prediksi":183,"hasil_prediksi":"Layak","probabilitas":0.9958546541623811,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.59%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif."}
2025-05-20 08:43:41 - Request received for ID: 2094
2025-05-20 08:43:41 - Nasabah data retrieved: {"id_nasabah":2094,"nama_nasabah":"Nur","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":30,"tujuan_pinjaman":"Renovasi","kelayakan":null,"created_at":"2025-05-20 15:43:30","updated_at":"2025-05-20 15:43:41","sumber_data":"leasing"}
2025-05-20 08:43:43 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999915190363995,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-20 08:43:43 - Prediction saved with ID: 189
2025-05-20 08:43:43 - Sending response: {"id_prediksi":189,"hasil_prediksi":"Layak","probabilitas":0.9999915190363995,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-20 08:46:37 - Request received for ID: 2095
2025-05-20 08:46:37 - Nasabah data retrieved: {"id_nasabah":2095,"nama_nasabah":"Udin","jenis_kelamin":"Laki-laki","status_perkawinan":"Cerai","pekerjaan":"Swasta","penghasilan":"3000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":43,"tujuan_pinjaman":"Modal Usaha","kelayakan":null,"created_at":"2025-05-20 15:46:14","updated_at":"2025-05-20 15:46:37","sumber_data":"leasing"}
2025-05-20 08:46:37 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.9853823412596983,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 1.46%. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, pekerjaan yang kurang stabil, status kepemilikan rumah yang kurang baik.","skor":0,"max_skor":100}
2025-05-20 08:46:37 - Prediction saved with ID: 192
2025-05-20 08:46:37 - Sending response: {"id_prediksi":192,"hasil_prediksi":"Tidak Layak","probabilitas":0.9853823412596983,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 1.46%. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, pekerjaan yang kurang stabil, status kepemilikan rumah yang kurang baik."}
2025-05-20 09:00:30 - Request received for ID: 2097
2025-05-20 09:00:30 - Nasabah data retrieved: {"id_nasabah":2097,"nama_nasabah":"Gita","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":45,"tujuan_pinjaman":"Renovasi","kelayakan":null,"created_at":"2025-05-20 16:00:21","updated_at":"2025-05-20 16:00:30","sumber_data":"leasing"}
2025-05-20 09:00:33 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999866720663079,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-20 09:00:33 - Prediction saved with ID: 195
2025-05-20 09:00:33 - Sending response: {"id_prediksi":195,"hasil_prediksi":"Layak","probabilitas":0.9999866720663079,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-20 09:14:02 - Request received for ID: 2100
2025-05-20 09:14:02 - Nasabah data retrieved: {"id_nasabah":2100,"nama_nasabah":"Nesa","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"3000000.00","jumlah_tanggungan":5,"jumlah_pinjaman":"30000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":37,"tujuan_pinjaman":"Modal Usaha","kelayakan":null,"created_at":"2025-05-20 16:13:53","updated_at":"2025-05-20 16:14:02","sumber_data":"leasing"}
2025-05-20 09:14:03 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9998964603091226,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.99%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-20 09:14:03 - Prediction saved with ID: 197
2025-05-20 09:14:03 - Sending response: {"id_prediksi":197,"hasil_prediksi":"Layak","probabilitas":0.9998964603091226,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.99%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-20 09:18:07 - Request received for ID: 2098
2025-05-20 09:18:07 - Nasabah data retrieved: {"id_nasabah":2098,"nama_nasabah":"Samsudin","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"3000000.00","jumlah_tanggungan":5,"jumlah_pinjaman":"25000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2020,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":45,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-20 16:11:17","updated_at":"2025-05-20 16:18:07","sumber_data":"leasing"}
2025-05-20 09:18:09 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9999932541694931,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-20 09:18:09 - Prediction saved with ID: 198
2025-05-20 09:18:09 - Sending response: {"id_prediksi":198,"hasil_prediksi":"Layak","probabilitas":0.9999932541694931,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 100.00%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-20 23:29:56 - Request received for ID: 2106
2025-05-20 23:29:56 - Nasabah data retrieved: {"id_nasabah":2106,"nama_nasabah":"Yati","jenis_kelamin":"Perempuan","status_perkawinan":"Cerai","pekerjaan":"Swasta","penghasilan":"4500000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"35000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2021,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":49,"tujuan_pinjaman":"Renovasi","kelayakan":null,"created_at":"2025-05-20 23:29:27","updated_at":"2025-05-20 23:29:56","sumber_data":"leasing"}
2025-05-20 23:30:07 - Error during prediction: GAGAL TERHUBUNG KE SERVER MODEL PYTHON!

Pastikan server model Python sudah dijalankan dengan menjalankan file start_model_server.bat terlebih dahulu.

Cara menjalankan server model Python:
1. Buka Command Prompt atau PowerShell
2. Navigasi ke direktori ini
3. Jalankan perintah: .\start_model_server.bat
4. Tunggu hingga server model Python berjalan (akan muncul pesan "Running on http://0.0.0.0:5000")
5. Biarkan jendela Command Prompt/PowerShell tetap terbuka

Error detail: Operation timed out after 10933 milliseconds with 0 bytes received
2025-05-20 23:35:06 - Request received for ID: 2107
2025-05-20 23:35:06 - Nasabah data retrieved: {"id_nasabah":2107,"nama_nasabah":"Fuad","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Wiraswasta","penghasilan":"4000000.00","jumlah_tanggungan":5,"jumlah_pinjaman":"35000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":43,"tujuan_pinjaman":"Modal Usaha","kelayakan":null,"created_at":"2025-05-20 23:34:35","updated_at":"2025-05-20 23:35:05","sumber_data":"leasing"}
2025-05-20 23:35:18 - Error during prediction: GAGAL TERHUBUNG KE SERVER MODEL PYTHON!

Pastikan server model Python sudah dijalankan dengan menjalankan file start_model_server.bat terlebih dahulu.

Cara menjalankan server model Python:
1. Buka Command Prompt atau PowerShell
2. Navigasi ke direktori ini
3. Jalankan perintah: .\start_model_server.bat
4. Tunggu hingga server model Python berjalan (akan muncul pesan "Running on http://0.0.0.0:5000")
5. Biarkan jendela Command Prompt/PowerShell tetap terbuka

Error detail: Operation timed out after 10000 milliseconds with 0 bytes received
2025-05-20 23:36:03 - Request received for ID: 2107
2025-05-20 23:36:03 - Nasabah data retrieved: {"id_nasabah":2107,"nama_nasabah":"Fuad","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Wiraswasta","penghasilan":"4000000.00","jumlah_tanggungan":5,"jumlah_pinjaman":"35000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2023,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":43,"tujuan_pinjaman":"Modal Usaha","kelayakan":"Tidak Layak","created_at":"2025-05-20 23:34:35","updated_at":"2025-05-20 23:35:32","sumber_data":"leasing"}
2025-05-20 23:36:12 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.9262090016329957,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 7.38%. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang banyak (5 orang), status kepemilikan rumah yang kurang baik.","skor":0,"max_skor":100}
2025-05-20 23:36:13 - Prediction saved with ID: 205
2025-05-20 23:36:13 - Sending response: {"id_prediksi":205,"hasil_prediksi":"Tidak Layak","probabilitas":0.9262090016329957,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 7.38%. Faktor yang perlu diperhatikan: rasio pinjaman terhadap penghasilan per kapita yang tinggi, jumlah tanggungan yang banyak (5 orang), status kepemilikan rumah yang kurang baik."}
2025-05-21 03:39:22 - Request received for ID: 2108
2025-05-21 03:39:22 - Nasabah data retrieved: {"id_nasabah":2108,"nama_nasabah":"Santi Rahayu","jenis_kelamin":"Perempuan","status_perkawinan":"Cerai","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"30000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2023,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":55,"tujuan_pinjaman":"Renovasi","kelayakan":null,"created_at":"2025-05-21 03:39:13","updated_at":"2025-05-21 03:39:22","sumber_data":"leasing"}
2025-05-21 03:39:25 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.9969615609221109,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.70%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-21 03:39:25 - Prediction saved with ID: 206
2025-05-21 03:39:25 - Sending response: {"id_prediksi":206,"hasil_prediksi":"Layak","probabilitas":0.9969615609221109,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 99.70%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif, status kepemilikan rumah yang baik."}
2025-05-21 07:39:39 - Request received for ID: 2124
2025-05-21 07:39:39 - Nasabah data retrieved: {"id_nasabah":2124,"nama_nasabah":"Aisyah","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Wiraswasta","penghasilan":"6000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"45000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2023,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Kontrak","umur":33,"tujuan_pinjaman":"Renovasi","kelayakan":null,"created_at":"2025-05-21 07:37:29","updated_at":"2025-05-21 07:38:34","sumber_data":"leasing"}
2025-05-21 07:39:45 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.6829365747544801,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 68.29%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif.","skor":0,"max_skor":100}
2025-05-21 07:39:45 - Prediction saved with ID: 221
2025-05-21 07:39:45 - Sending response: {"id_prediksi":221,"hasil_prediksi":"Layak","probabilitas":0.6829365747544801,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 68.29%. Faktor yang mendukung: jaminan BPKB motor dengan pajak aktif."}
2025-05-21 15:00:19 - Request received for ID: 2132
2025-05-21 15:00:19 - Nasabah data retrieved: {"id_nasabah":2132,"nama_nasabah":"Yuli","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":4,"jumlah_pinjaman":"50000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2021,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":43,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-21 10:20:37","updated_at":"2025-05-21 11:33:35","sumber_data":"leasing"}
2025-05-21 15:00:21 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.3,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 70.00%. Faktor yang perlu diperhatikan: rasio angsuran terhadap penghasilan per kapita terlalu tinggi (3.73), rasio pinjaman terhadap penghasilan per kapita tahunan terlalu tinggi (3.33), jumlah tanggungan yang banyak (4 orang), pekerjaan yang kurang stabil, nilai jaminan tidak mencukupi untuk pinjaman (estimasi 0.50x).","skor":0,"max_skor":100}
2025-05-21 15:00:21 - Prediction saved with ID: 237
2025-05-21 15:00:21 - Sending response: {"id_prediksi":237,"hasil_prediksi":"Tidak Layak","probabilitas":0.3,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 70.00%. Faktor yang perlu diperhatikan: rasio angsuran terhadap penghasilan per kapita terlalu tinggi (3.73), rasio pinjaman terhadap penghasilan per kapita tahunan terlalu tinggi (3.33), jumlah tanggungan yang banyak (4 orang), pekerjaan yang kurang stabil, nilai jaminan tidak mencukupi untuk pinjaman (estimasi 0.50x)."}
2025-05-22 04:13:18 - Request received for ID: 2131
2025-05-22 04:13:18 - Nasabah data retrieved: {"id_nasabah":2131,"nama_nasabah":"Wiliam","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"5000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"50000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2021,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":32,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-21 10:06:15","updated_at":"2025-05-22 04:13:18","sumber_data":"leasing"}
2025-05-22 04:13:28 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.3,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 70.00%. Rasio pinjaman terhadap penghasilan per kapita bulanan adalah 30.00 kali, yang melebihi batas wajar (di atas 8.4 kali penghasilan bulanan).","skor":45,"max_skor":100}
2025-05-22 04:13:28 - Prediction saved with ID: 238
2025-05-22 04:13:28 - Sending response: {"id_prediksi":238,"hasil_prediksi":"Tidak Layak","probabilitas":0.3,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 70.00%. Rasio pinjaman terhadap penghasilan per kapita bulanan adalah 30.00 kali, yang melebihi batas wajar (di atas 8.4 kali penghasilan bulanan)."}
2025-05-22 05:17:13 - Request received for ID: 2130
2025-05-22 05:17:13 - Nasabah data retrieved: {"id_nasabah":2130,"nama_nasabah":"Wiliam","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"4000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"50000000.00","jangka_waktu":12,"jaminan":"BPKB Motor","tahun_kendaraan":2021,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":32,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-21 10:04:56","updated_at":"2025-05-22 05:17:13","sumber_data":"leasing"}
2025-05-22 05:17:16 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.01,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 99.00%. Angsuran bulanan diperkirakan Rp 4.442.439 selama 12 bulan. Faktor yang perlu diperhatikan: angsuran bulanan terlalu besar dibandingkan penghasilan (mencapai 111% dari penghasilan bulanan), rasio pinjaman terhadap penghasilan tahunan terlalu tinggi (1.04 atau 104.2%), pekerjaan yang kurang stabil, nilai jaminan tidak mencukupi untuk pinjaman (estimasi 0.30x). Saran: Jaminan BPKB Motor hanya dapat digunakan untuk pinjaman maksimal Rp 20.000.000. Pertimbangkan untuk mengurangi jumlah pinjaman atau menggunakan jaminan lain seperti BPKB Mobil.","skor":0,"max_skor":100}
2025-05-22 05:17:16 - Prediction saved with ID: 239
2025-05-22 05:17:16 - Sending response: {"id_prediksi":239,"hasil_prediksi":"Tidak Layak","probabilitas":0.01,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 99.00%. Angsuran bulanan diperkirakan Rp 4.442.439 selama 12 bulan. Faktor yang perlu diperhatikan: angsuran bulanan terlalu besar dibandingkan penghasilan (mencapai 111% dari penghasilan bulanan), rasio pinjaman terhadap penghasilan tahunan terlalu tinggi (1.04 atau 104.2%), pekerjaan yang kurang stabil, nilai jaminan tidak mencukupi untuk pinjaman (estimasi 0.30x). Saran: Jaminan BPKB Motor hanya dapat digunakan untuk pinjaman maksimal Rp 20.000.000. Pertimbangkan untuk mengurangi jumlah pinjaman atau menggunakan jaminan lain seperti BPKB Mobil."}
2025-05-22 07:55:16 - Request received for ID: 2143
2025-05-22 07:55:16 - Nasabah data retrieved: {"id_nasabah":2143,"nama_nasabah":"Rio","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Lainnya","penghasilan":"5500000.00","jumlah_tanggungan":1,"jumlah_pinjaman":"85000000.00","jangka_waktu":12,"jaminan":"BPKB Mobil","tahun_kendaraan":2019,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":34,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-22 07:45:32","updated_at":"2025-05-22 07:55:14","sumber_data":"leasing"}
2025-05-22 07:55:19 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.09000000000000008,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 91.00%. Angsuran bulanan diperkirakan Rp 7.552.147 selama 12 bulan. Faktor yang perlu diperhatikan: angsuran bulanan terlalu besar dibandingkan penghasilan (mencapai 137% dari penghasilan bulanan), rasio pinjaman terhadap penghasilan tahunan terlalu tinggi (1.29 atau 128.8%), pekerjaan yang kurang stabil, nilai jaminan tidak mencukupi untuk pinjaman (estimasi 0.94x). Saran: Jumlah pinjaman terlalu besar dibandingkan penghasilan. Pertimbangkan untuk mengurangi jumlah pinjaman menjadi sekitar Rp 66.758.232 atau meningkatkan penghasilan.","skor":0,"max_skor":100}
2025-05-22 07:55:19 - Prediction saved with ID: 243
2025-05-22 07:55:19 - Sending response: {"id_prediksi":243,"hasil_prediksi":"Tidak Layak","probabilitas":0.09000000000000008,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 91.00%. Angsuran bulanan diperkirakan Rp 7.552.147 selama 12 bulan. Faktor yang perlu diperhatikan: angsuran bulanan terlalu besar dibandingkan penghasilan (mencapai 137% dari penghasilan bulanan), rasio pinjaman terhadap penghasilan tahunan terlalu tinggi (1.29 atau 128.8%), pekerjaan yang kurang stabil, nilai jaminan tidak mencukupi untuk pinjaman (estimasi 0.94x). Saran: Jumlah pinjaman terlalu besar dibandingkan penghasilan. Pertimbangkan untuk mengurangi jumlah pinjaman menjadi sekitar Rp 66.758.232 atau meningkatkan penghasilan."}
2025-05-22 15:59:18 - Request received for ID: 2155
2025-05-22 15:59:18 - Nasabah data retrieved: {"id_nasabah":2155,"nama_nasabah":"Tri","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"8000000.00","jumlah_tanggungan":2,"jumlah_pinjaman":"25000000.00","jangka_waktu":24,"jaminan":"BPKB Motor","tahun_kendaraan":2024,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":43,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-22 15:58:34","updated_at":"2025-05-22 15:59:18","sumber_data":"leasing"}
2025-05-22 15:59:28 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.7,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 70.00%. Jumlah pinjaman yang diajukan (Rp 25.000.000) masih dalam batas wajar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 48.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 52%, yang masih dalam batas aman (di bawah 70%).","skor":75,"max_skor":100}
2025-05-22 15:59:28 - Prediction saved with ID: 251
2025-05-22 15:59:28 - Sending response: {"id_prediksi":251,"hasil_prediksi":"Layak","probabilitas":0.7,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 70.00%. Jumlah pinjaman yang diajukan (Rp 25.000.000) masih dalam batas wajar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 48.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 52%, yang masih dalam batas aman (di bawah 70%)."}
2025-05-22 19:59:48 - Request received for ID: NULL
2025-05-22 19:59:49 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:00:43 - Request received for ID: 2154
2025-05-22 20:00:43 - Nasabah data retrieved: {"id_nasabah":2154,"nama_nasabah":"Tri","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"8000000.00","jumlah_tanggungan":2,"jumlah_pinjaman":"25000000.00","jangka_waktu":24,"jaminan":"BPKB Motor","tahun_kendaraan":2024,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":43,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-22 15:38:31","updated_at":"2025-05-22 20:00:43","sumber_data":"leasing"}
2025-05-22 20:00:45 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.7,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 70.00%. Jumlah pinjaman yang diajukan (Rp 25.000.000) masih dalam batas wajar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 48.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 52%, yang masih dalam batas aman (di bawah 70%).","skor":75,"max_skor":100}
2025-05-22 20:00:45 - Prediction saved with ID: 253
2025-05-22 20:00:45 - Sending response: {"id_prediksi":253,"hasil_prediksi":"Layak","probabilitas":0.7,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 70.00%. Jumlah pinjaman yang diajukan (Rp 25.000.000) masih dalam batas wajar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 48.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 52%, yang masih dalam batas aman (di bawah 70%)."}
2025-05-22 20:03:12 - Request received for ID: 2157
2025-05-22 20:03:12 - Nasabah data retrieved: {"id_nasabah":2157,"nama_nasabah":"Susi","jenis_kelamin":"Perempuan","status_perkawinan":"Menikah","pekerjaan":"Wiraswasta","penghasilan":"6000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"25000000.00","jangka_waktu":24,"jaminan":"BPKB Motor","tahun_kendaraan":2021,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":36,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-22 20:03:00","updated_at":"2025-05-22 20:03:12","sumber_data":"leasing"}
2025-05-22 20:03:14 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.3,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 70.00%. Jumlah pinjaman yang diajukan (Rp 25.000.000) terlalu besar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 24.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 104%, yang melebihi batas aman (di atas 70%).","skor":45,"max_skor":100}
2025-05-22 20:03:14 - Prediction saved with ID: 254
2025-05-22 20:03:14 - Sending response: {"id_prediksi":254,"hasil_prediksi":"Tidak Layak","probabilitas":0.3,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 70.00%. Jumlah pinjaman yang diajukan (Rp 25.000.000) terlalu besar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 24.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 104%, yang melebihi batas aman (di atas 70%)."}
2025-05-22 20:06:22 - Request received for ID: 2158
2025-05-22 20:06:22 - Nasabah data retrieved: {"id_nasabah":2158,"nama_nasabah":"Beni","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"PNS","penghasilan":"10000000.00","jumlah_tanggungan":3,"jumlah_pinjaman":"45000000.00","jangka_waktu":24,"jaminan":"BPKB Mobil","tahun_kendaraan":2023,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":33,"tujuan_pinjaman":"Modal Usaha","kelayakan":null,"created_at":"2025-05-22 20:06:08","updated_at":"2025-05-22 20:06:22","sumber_data":"leasing"}
2025-05-22 20:06:25 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.3,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 70.00%. Jumlah pinjaman yang diajukan (Rp 45.000.000) terlalu besar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 40.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 113%, yang melebihi batas aman (di atas 70%).","skor":45,"max_skor":100}
2025-05-22 20:06:25 - Prediction saved with ID: 255
2025-05-22 20:06:25 - Sending response: {"id_prediksi":255,"hasil_prediksi":"Tidak Layak","probabilitas":0.3,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 70.00%. Jumlah pinjaman yang diajukan (Rp 45.000.000) terlalu besar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 40.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 113%, yang melebihi batas aman (di atas 70%)."}
2025-05-22 20:14:54 - Request received for ID: 2159
2025-05-22 20:14:54 - Nasabah data retrieved: {"id_nasabah":2159,"nama_nasabah":"Rudi","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Wiraswasta","penghasilan":"6500000.00","jumlah_tanggungan":2,"jumlah_pinjaman":"30000000.00","jangka_waktu":24,"jaminan":"BPKB Motor","tahun_kendaraan":2025,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":43,"tujuan_pinjaman":"Modal Usaha\r\n","kelayakan":null,"created_at":"2025-05-22 20:13:15","updated_at":"2025-05-22 20:14:54","sumber_data":"leasing"}
2025-05-22 20:14:56 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.09890109890109904,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 90.11%. Jumlah pinjaman yang diajukan (Rp 30.000.000) terlalu besar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 39.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 77%, yang melebihi batas aman (di atas 70%).","skor":45,"max_skor":100}
2025-05-22 20:14:56 - Prediction saved with ID: 256
2025-05-22 20:14:56 - Sending response: {"id_prediksi":256,"hasil_prediksi":"Tidak Layak","probabilitas":0.09890109890109904,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 90.11%. Jumlah pinjaman yang diajukan (Rp 30.000.000) terlalu besar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 39.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 77%, yang melebihi batas aman (di atas 70%)."}
2025-05-22 20:29:59 - Request received for ID: NULL
2025-05-22 20:29:59 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:30:00 - Request received for ID: NULL
2025-05-22 20:30:00 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:30:01 - Request received for ID: NULL
2025-05-22 20:30:01 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:30:02 - Request received for ID: NULL
2025-05-22 20:30:02 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:30:02 - Request received for ID: NULL
2025-05-22 20:30:02 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:30:03 - Request received for ID: NULL
2025-05-22 20:30:03 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:30:03 - Request received for ID: NULL
2025-05-22 20:30:03 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:30:04 - Request received for ID: NULL
2025-05-22 20:30:04 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:30:58 - Request received for ID: NULL
2025-05-22 20:30:58 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:30:58 - Request received for ID: NULL
2025-05-22 20:30:58 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:30:59 - Request received for ID: NULL
2025-05-22 20:30:59 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:30:59 - Request received for ID: NULL
2025-05-22 20:30:59 - Error: ID Nasabah tidak ditemukan
2025-05-22 20:30:59 - Request received for ID: NULL
2025-05-22 20:30:59 - Error: ID Nasabah tidak ditemukan
2025-05-23 10:08:21 - Request received for ID: 2173
2025-05-23 10:08:21 - Nasabah data retrieved: {"id_nasabah":2173,"nama_nasabah":"Peter Parker","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"8000000.00","jumlah_tanggungan":2,"jumlah_pinjaman":"50000000.00","jangka_waktu":24,"jaminan":"BPKB Mobil","tahun_kendaraan":2025,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":34,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-23 09:51:38","updated_at":"2025-05-23 10:08:21","sumber_data":"leasing"}
2025-05-23 10:08:31 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.3,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 70.00%. Jumlah pinjaman yang diajukan (Rp 50.000.000) terlalu besar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 48.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 104%, yang melebihi batas aman (di atas 90%). Rekomendasi: Pertimbangkan untuk mengajukan pinjaman yang lebih kecil (sekitar Rp 38.400.000) atau meningkatkan penghasilan untuk meningkatkan kelayakan kredit.","skor":45,"max_skor":100}
2025-05-23 10:08:31 - Prediction saved with ID: 268
2025-05-23 10:08:31 - Sending response: {"id_prediksi":268,"hasil_prediksi":"Tidak Layak","probabilitas":0.3,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 70.00%. Jumlah pinjaman yang diajukan (Rp 50.000.000) terlalu besar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 48.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 104%, yang melebihi batas aman (di atas 90%). Rekomendasi: Pertimbangkan untuk mengajukan pinjaman yang lebih kecil (sekitar Rp 38.400.000) atau meningkatkan penghasilan untuk meningkatkan kelayakan kredit."}
2025-05-23 13:51:27 - Request received for ID: NULL
2025-05-23 13:51:27 - Error: ID Nasabah tidak ditemukan
2025-05-23 19:50:49 - Request received for ID: 2172
2025-05-23 19:50:49 - Nasabah data retrieved: {"id_nasabah":2172,"nama_nasabah":"Peter Parker","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"8000000.00","jumlah_tanggungan":2,"jumlah_pinjaman":"50000000.00","jangka_waktu":24,"jaminan":"BPKB Mobil","tahun_kendaraan":2025,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":34,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-23 09:49:51","updated_at":"2025-05-23 19:50:48","sumber_data":"leasing"}
2025-05-23 19:50:51 - Prediction result: {"hasil_prediksi":"Tidak Layak","probabilitas":0.3,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 70.00%. Jumlah pinjaman yang diajukan (Rp 50.000.000) terlalu besar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 48.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 104%, yang melebihi batas aman (di atas 90%). Rekomendasi: Pertimbangkan untuk mengajukan pinjaman yang lebih kecil (sekitar Rp 38.400.000) atau meningkatkan penghasilan untuk meningkatkan kelayakan kredit.","skor":45,"max_skor":100}
2025-05-23 19:50:52 - Prediction saved with ID: 275
2025-05-23 19:50:52 - Sending response: {"id_prediksi":275,"hasil_prediksi":"Tidak Layak","probabilitas":0.3,"keterangan":"Nasabah diprediksi tidak layak menerima kredit dengan probabilitas 70.00%. Jumlah pinjaman yang diajukan (Rp 50.000.000) terlalu besar dibandingkan dengan penghasilan per kapita tahunan nasabah (Rp 48.000.000 per tahun). Rasio pinjaman terhadap penghasilan per kapita tahunan adalah 104%, yang melebihi batas aman (di atas 90%). Rekomendasi: Pertimbangkan untuk mengajukan pinjaman yang lebih kecil (sekitar Rp 38.400.000) atau meningkatkan penghasilan untuk meningkatkan kelayakan kredit."}
2025-05-23 22:13:36 - Request received for ID: NULL
2025-05-23 22:13:36 - Error: ID Nasabah tidak ditemukan
2025-05-23 22:16:43 - Request received for ID: 2171
2025-05-23 22:16:43 - Nasabah data retrieved: {"id_nasabah":2171,"nama_nasabah":"Peter Parker","jenis_kelamin":"Laki-laki","status_perkawinan":"Menikah","pekerjaan":"Swasta","penghasilan":"8000000.00","jumlah_tanggungan":2,"jumlah_pinjaman":"50000000.00","jangka_waktu":24,"jaminan":"BPKB Mobil","tahun_kendaraan":2025,"harga_kendaraan":null,"status_pajak":"Aktif","kepemilikan_rumah":"Milik Sendiri","umur":34,"tujuan_pinjaman":"Kebutuhan Pribadi","kelayakan":null,"created_at":"2025-05-23 09:49:04","updated_at":"2025-05-23 22:16:43","sumber_data":"leasing"}
2025-05-23 22:16:45 - Prediction result: {"hasil_prediksi":"Layak","probabilitas":0.7,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 70.00%. Angsuran bulanan diperkirakan Rp 2.353.674 selama 24 bulan. Faktor yang mendukung: rasio angsuran terhadap penghasilan baik (0.29 atau 29.4%), jangka waktu pinjaman sedang (24 bulan), jaminan BPKB mobil dengan kondisi baik dan pajak aktif, nilai jaminan mencukupi untuk pinjaman (estimasi 3.00%), status kepemilikan rumah yang baik.","skor":0,"max_skor":100}
2025-05-23 22:16:45 - Prediction saved with ID: 277
2025-05-23 22:16:45 - Sending response: {"id_prediksi":277,"hasil_prediksi":"Layak","probabilitas":0.7,"keterangan":"Nasabah diprediksi layak menerima kredit dengan probabilitas 70.00%. Angsuran bulanan diperkirakan Rp 2.353.674 selama 24 bulan. Faktor yang mendukung: rasio angsuran terhadap penghasilan baik (0.29 atau 29.4%), jangka waktu pinjaman sedang (24 bulan), jaminan BPKB mobil dengan kondisi baik dan pajak aktif, nilai jaminan mencukupi untuk pinjaman (estimasi 3.00%), status kepemilikan rumah yang baik."}
2025-06-11 02:19:48 - Request received for ID: NULL
2025-06-11 02:19:48 - Error: ID Nasabah tidak ditemukan
2025-06-11 02:53:58 - Request received for ID: NULL
2025-06-11 02:53:58 - Error: ID Nasabah tidak ditemukan
2025-06-11 02:55:11 - Request received for ID: NULL
2025-06-11 02:55:11 - Error: ID Nasabah tidak ditemukan
2025-06-11 02:56:36 - Request received for ID: NULL
2025-06-11 02:56:36 - Error: ID Nasabah tidak ditemukan
2025-06-11 03:02:47 - Request received for ID: NULL
2025-06-11 03:02:47 - Error: ID Nasabah tidak ditemukan

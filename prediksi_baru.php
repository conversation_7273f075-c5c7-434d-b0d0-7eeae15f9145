<?php
// <PERSON><PERSON> output buffering
ob_start();

include 'koneksi.php';
include 'cek_session.php';
// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);
require_once 'nav_analis.php';

// Fungsi untuk memformat angka menjadi format rupiah
function formatRupiah($angka) {
    return "Rp " . number_format($angka, 0, ',', '.');
}

// Inisialisasi variabel
$error_message = '';
$success_message = '';
$show_prediction_result = false;
$prediction_result = null;

// Log untuk debugging
error_log("Halaman prediksi_baru.php diakses");
$form_data = [
    'nama_nasabah' => '',
    'jenis_kelamin' => 'Laki-laki',
    'status_perkawinan' => 'Menikah',
    'pekerjaan' => 'Swasta',
    'penghasilan' => '5.000.000',
    'jumlah_tanggungan' => '',
    'jumlah_pinjaman' => '10.000.000',
    'jangka_waktu' => '12',
    'jaminan' => 'BPKB Motor',
    'tahun_kendaraan' => date('Y'),
    'status_pajak' => 'Aktif',
    'kepemilikan_rumah' => '',
    'umur' => '',
    'tujuan_pinjaman' => 'Kebutuhan Pribadi'
];

// Cek apakah ada pesan error dari proses_prediksi_leasing.php
if (isset($_GET['error'])) {
    $error_message = $_GET['error'];
}

// Cek apakah ada parameter id_nasabah dari halaman lain
$selected_nasabah = isset($_GET['id_nasabah']) ? $_GET['id_nasabah'] : null;

// Tambahkan default value untuk tahun_kendaraan dan status_pajak
if (empty($form_data['tahun_kendaraan'])) {
    $form_data['tahun_kendaraan'] = '';
}
if (empty($form_data['status_pajak'])) {
    $form_data['status_pajak'] = '';
}

// Proses form jika ada submit
if (isset($_POST['submit'])) {
    // Tambahkan log untuk debugging
    error_log("Form submitted: " . print_r($_POST, true));

    // Ambil data dari form
    $form_data = [
        'nama_nasabah' => $_POST['nama_nasabah'] ?? '',
        'jenis_kelamin' => $_POST['jenis_kelamin'] ?? '',
        'status_perkawinan' => $_POST['status_perkawinan'] ?? '',
        'pekerjaan' => $_POST['pekerjaan'] ?? '',
        'penghasilan' => $_POST['penghasilan'] ?? '',
        'jumlah_tanggungan' => $_POST['jumlah_tanggungan'] ?? '',
        'jumlah_pinjaman' => $_POST['jumlah_pinjaman'] ?? '',
        'jangka_waktu' => $_POST['jangka_waktu'] ?? '',
        'jaminan' => $_POST['jaminan'] ?? 'BPKB Motor',
        'tahun_kendaraan' => $_POST['tahun_kendaraan'] ?? (date('Y')),
        'status_pajak' => $_POST['status_pajak'] ?? '',
        'kepemilikan_rumah' => $_POST['kepemilikan_rumah'] ?? '',
        'umur' => $_POST['umur'] ?? '',
        'tujuan_pinjaman' => $_POST['tujuan_pinjaman'] ?? ''
    ];

    // Validasi input
    if (empty($form_data['nama_nasabah'])) {
        $error_message = "Nama nasabah harus diisi!";
    } elseif (empty($form_data['penghasilan'])) {
        $error_message = "Penghasilan harus diisi!";
    } elseif (empty($form_data['jumlah_tanggungan']) || $form_data['jumlah_tanggungan'] < 0) {
        $error_message = "Jumlah tanggungan harus diisi dengan nilai minimal 0!";
    } elseif (empty($form_data['jumlah_pinjaman'])) {
        $error_message = "Jumlah pinjaman harus diisi!";
    } else {
        // Validasi data untuk tahun kendaraan dan status pajak
        // Pastikan tahun kendaraan diisi dan valid
        if (empty($form_data['tahun_kendaraan'])) {
            $error_message = "Tahun kendaraan harus diisi!";
        }
        elseif ($form_data['tahun_kendaraan'] < 1990 || $form_data['tahun_kendaraan'] > date('Y')) {
            $error_message = "Tahun kendaraan tidak valid! Harus antara 1990 dan " . date('Y');
        }
        // Pastikan status pajak diisi
        elseif (empty($form_data['status_pajak'])) {
            $error_message = "Status pajak kendaraan harus diisi!";
        }

        // Jika tidak ada error message, lanjutkan proses
        if (empty($error_message)) {
            // Simpan data nasabah ke database
            $sql = "INSERT INTO nasabah (nama_nasabah, jenis_kelamin, status_perkawinan, pekerjaan, penghasilan,
                    jumlah_tanggungan, jumlah_pinjaman, jangka_waktu, jaminan, tahun_kendaraan, status_pajak,
                    kepemilikan_rumah, umur, tujuan_pinjaman)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            // Gunakan tujuan pinjaman dari form jika diisi, jika tidak set default
            $tujuan_pinjaman = !empty($form_data['tujuan_pinjaman']) ? $form_data['tujuan_pinjaman'] : 'Kebutuhan Pribadi';

            // Konversi nilai numerik ke tipe yang sesuai
            // Hapus titik sebagai pemisah ribuan pada penghasilan dan jumlah pinjaman
            $penghasilan = (float)str_replace('.', '', $form_data['penghasilan']);
            $jumlah_tanggungan = (int)$form_data['jumlah_tanggungan'];
            $jumlah_pinjaman = (float)str_replace('.', '', $form_data['jumlah_pinjaman']);
            $jangka_waktu = (int)$form_data['jangka_waktu'];
            $umur = (int)$form_data['umur'];
            $tahun_kendaraan = !empty($form_data['tahun_kendaraan']) ? $form_data['tahun_kendaraan'] : null;

            // Tambahkan error handling untuk debugging
            error_log("SQL Query: " . $sql);

            $stmt = mysqli_prepare($koneksi, $sql);
            if (!$stmt) {
                error_log("Error preparing statement: " . mysqli_error($koneksi));
                $error_message = "Gagal menyiapkan query: " . mysqli_error($koneksi);
            } else {
                mysqli_stmt_bind_param($stmt, "ssssdidissssis",
                    $form_data['nama_nasabah'],
                    $form_data['jenis_kelamin'],
                    $form_data['status_perkawinan'],
                    $form_data['pekerjaan'],
                    $penghasilan,
                    $jumlah_tanggungan,
                    $jumlah_pinjaman,
                    $jangka_waktu,
                    $form_data['jaminan'],
                    $tahun_kendaraan,
                    $form_data['status_pajak'],
                    $form_data['kepemilikan_rumah'],
                    $umur,
                    $tujuan_pinjaman
                );
            }

            if (mysqli_stmt_execute($stmt)) {
                $id_nasabah = mysqli_insert_id($koneksi);

                // Lakukan prediksi menggunakan API model Python
                error_log("Melakukan prediksi untuk nasabah ID: " . $id_nasabah);

                // Ambil data nasabah
                $query_nasabah = "SELECT * FROM nasabah WHERE id_nasabah = ?";
                $stmt_nasabah = mysqli_prepare($koneksi, $query_nasabah);
                mysqli_stmt_bind_param($stmt_nasabah, "i", $id_nasabah);
                mysqli_stmt_execute($stmt_nasabah);
                $result_nasabah = mysqli_stmt_get_result($stmt_nasabah);
                $nasabah_data = mysqli_fetch_assoc($result_nasabah);

                if ($nasabah_data) {
                    try {
                        error_log("Data nasabah ditemukan: " . print_r($nasabah_data, true));

                        // Gunakan api_predict.php untuk melakukan prediksi
                        require_once 'api_predict.php';

                        error_log("Memulai prediksi dengan backpropagation...");

                        // Lakukan prediksi dengan model backpropagation
                        $hasil_prediksi = prediksi_dengan_backpropagation($nasabah_data);

                        error_log("Hasil prediksi: " . print_r($hasil_prediksi, true));

                        // Simpan hasil prediksi ke database
                        $id_prediksi = simpan_hasil_prediksi($nasabah_data, $hasil_prediksi);

                        error_log("ID prediksi yang disimpan: " . $id_prediksi);

                        if ($id_prediksi) {
                            $success_message = "Data nasabah berhasil disimpan dan prediksi berhasil dilakukan.";
                            error_log("Prediksi berhasil, redirect ke hasil_prediksi.php");

                            // Redirect ke halaman hasil prediksi
                            header("Location: hasil_prediksi.php?id_nasabah=$id_nasabah&id_prediksi=$id_prediksi");
                            exit;
                        } else {
                            $error_message = "Gagal menyimpan hasil prediksi.";
                            error_log("Gagal menyimpan hasil prediksi");
                        }
                    } catch (Exception $e) {
                        $error_message = "Error saat melakukan prediksi: " . $e->getMessage();
                        error_log("Error prediksi: " . $e->getMessage());
                        error_log("Stack trace: " . $e->getTraceAsString());
                    }
                } else {
                    $error_message = "Data nasabah tidak ditemukan.";
                    error_log("Data nasabah tidak ditemukan setelah insert");
                }
            } else {
                $error_message = "Gagal menyimpan data nasabah: " . mysqli_error($koneksi);
            }
        }
    }
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Prediksi Kelayakan Kredit Nasabah Baru</h1>
            </div>
        </div>

        <!-- Tampilkan pesan error jika ada -->
        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fa fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <!-- Tampilkan pesan sukses jika ada -->
        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fa fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4>Form Tambah Nasabah dan Prediksi Kelayakan</h4>
            </div>
            <div class="panel-body">
                <form method="post" action="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Nama Nasabah *</label>
                                <input type="text" name="nama_nasabah" class="form-control"
                                       value="<?php echo htmlspecialchars($form_data['nama_nasabah']); ?>" required>
                            </div>

                            <div class="form-group">
                                <label>Jenis Kelamin *</label>
                                <select name="jenis_kelamin" class="form-control" required>
                                    <option value="">-- Pilih Jenis Kelamin --</option>
                                    <option value="Laki-laki" <?php echo $form_data['jenis_kelamin'] == 'Laki-laki' ? '' : ''; ?>>Laki-laki</option>
                                    <option value="Perempuan" <?php echo $form_data['jenis_kelamin'] == 'Perempuan' ? '' : ''; ?>>Perempuan</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>Status Perkawinan *</label>
                                <select name="status_perkawinan" class="form-control" required>
                                    <option value="">-- Pilih Status Perkawinan --</option>
                                    <option value="Menikah" <?php echo $form_data['status_perkawinan'] == 'Menikah' ? '' : ''; ?>>Menikah</option>
                                    <option value="Belum Menikah" <?php echo $form_data['status_perkawinan'] == 'Belum Menikah' ? '' : ''; ?>>Belum Menikah</option>
                                    <option value="Cerai" <?php echo $form_data['status_perkawinan'] == 'Cerai' ? '' : ''; ?>>Cerai</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>Pekerjaan *</label>
                                <select name="pekerjaan" class="form-control" required>
                                    <option value="">-- Pilih Pekerjaan --</option>
                                    <option value="PNS" <?php echo $form_data['pekerjaan'] == 'PNS' ? '' : ''; ?>>PNS</option>
                                    <option value="Swasta" <?php echo $form_data['pekerjaan'] == 'Swasta' ? '' : ''; ?>>Swasta</option>
                                    <option value="Wiraswasta" <?php echo $form_data['pekerjaan'] == 'Wiraswasta' ? '' : ''; ?>>Wiraswasta</option>
                                    <option value="Lainnya" <?php echo $form_data['pekerjaan'] == 'Lainnya' ? '' : ''; ?>>Lainnya</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>Umur *</label>
                                <input type="number" name="umur" class="form-control" min="20" max="60"
                                       value="<?php echo htmlspecialchars($form_data['umur']); ?>" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Penghasilan per Bulan (Rp) *</label>
                                <input type="text" class="form-control currency-input" id="penghasilan" name="penghasilan" required>
                            </div>

                            <div class="form-group">
                                <label>Jumlah Tanggungan *</label>
                                <input type="number" name="jumlah_tanggungan" class="form-control" min="0" max="10"
                                       value="<?php echo htmlspecialchars($form_data['jumlah_tanggungan']); ?>" required>
                                <small class="text-muted">Jumlah anggota keluarga yang menjadi tanggungan</small>
                            </div>

                            <div class="form-group">
                                <label>Jumlah Pinjaman (Rp) *</label>
                                <input type="text" class="form-control currency-input" id="jumlah_pinjaman" name="jumlah_pinjaman" required>
                            </div>

                            <div class="form-group">
                                <label>Jangka Waktu (bulan) *</label>
                                <input type="number" name="jangka_waktu" class="form-control" min="6" max="60"
                                       value="<?php echo htmlspecialchars($form_data['jangka_waktu']); ?>" required>
                            </div>

                            <div class="form-group">
                                <label>Jaminan *</label>
                                <select name="jaminan" class="form-control" id="jaminan-select" required>
                                    <option value="">-- Pilih Jaminan --</option>
                                    <option value="BPKB Motor" <?php echo $form_data['jaminan'] == 'BPKB Motor' ? '' : ''; ?>>BPKB Motor</option>
                                    <option value="BPKB Mobil" <?php echo $form_data['jaminan'] == 'BPKB Mobil' ? '' : ''; ?>>BPKB Mobil</option>
                                </select>
                                <small class="text-muted">Jaminan hanya berupa BPKB kendaraan (mobil/motor)</small>
                            </div>

                            <div class="form-group" id="tahun-kendaraan-group">
                                <label>Tahun Kendaraan *</label>
                                <input type="number" name="tahun_kendaraan" class="form-control" min="1990" max="<?php echo date('Y'); ?>"
                                       value="<?php echo htmlspecialchars($form_data['tahun_kendaraan']); ?>" required>
                                <small class="text-muted">Tahun pembuatan kendaraan sesuai STNK</small>
                            </div>

                            <div class="form-group" id="status-pajak-group">
                                <label>Status Pajak Kendaraan *</label>
                                <select name="status_pajak" class="form-control" required>
                                    <option value="">-- Pilih Status Pajak --</option>
                                    <option value="Aktif" <?php echo $form_data['status_pajak'] == 'Aktif' ? '' : ''; ?>>Aktif</option>
                                    <option value="Tidak Aktif" <?php echo $form_data['status_pajak'] == 'Tidak Aktif' ? '' : ''; ?>>Tidak Aktif</option>
                                </select>
                                <small class="text-muted">Status pajak kendaraan saat ini</small>
                            </div>

                            <div class="form-group">
                                <label>Kepemilikan Rumah *</label>
                                <select name="kepemilikan_rumah" class="form-control" required>
                                    <option value="">-- Pilih Status Kepemilikan Rumah --</option>
                                    <option value="Milik Sendiri" <?php echo $form_data['kepemilikan_rumah'] == 'Milik Sendiri' ? '' : ''; ?>>Milik Sendiri</option>
                                    <option value="Kontrak" <?php echo $form_data['kepemilikan_rumah'] == 'Kontrak' ? '' : ''; ?>>Kontrak</option>
                                    <option value="Orang Tua" <?php echo $form_data['kepemilikan_rumah'] == 'Orang Tua' ? '' : ''; ?>>Orang Tua</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>Tujuan Pinjaman</label>
                                <textarea name="tujuan_pinjaman" class="form-control" rows="2"><?php echo htmlspecialchars($form_data['tujuan_pinjaman'] ?? ''); ?></textarea>
                                <small class="text-muted">Opsional. Jika kosong akan diisi "Kebutuhan Pribadi"</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" name="submit" class="btn btn-primary">
                            <i class="fa fa-save"></i> Simpan & Prediksi Kelayakan
                        </button>
                        <a href="data_nasabah_analis.php" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<script>
document.addEventListener('DOMContentLoaded', function() {
    // Pastikan field tahun kendaraan dan status pajak selalu required
    var tahunKendaraan = document.getElementsByName('tahun_kendaraan')[0];
    var statusPajak = document.getElementsByName('status_pajak')[0];

    if (tahunKendaraan) tahunKendaraan.setAttribute('required', 'required');
    if (statusPajak) statusPajak.setAttribute('required', 'required');
});
</script>

<?php
require_once 'foot.php';

// Akhiri output buffering dan kirim output ke browser
ob_end_flush();
?>

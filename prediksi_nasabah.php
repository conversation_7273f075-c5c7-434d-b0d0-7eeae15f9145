<?php
// Aktifkan output buffering
ob_start();

// Include file koneksi database dan helper
include 'koneksi.php';
include 'date_helper.php';

// Pastikan session sudah dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include file cek session untuk memastikan user sudah login
include 'cek_session.php';

// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);

// Include file navigasi untuk analis
require_once 'nav_analis.php';

$error_message = '';
$success_message = '';

// Query untuk mengambil data nasabah yang berasal dari website dan belum diprediksi
$sql = "SELECT n.* 
        FROM nasabah n 
        LEFT JOIN hasil_prediksi hp ON n.id_nasabah = hp.id_nasabah 
        WHERE n.sumber = 'website' 
        AND hp.id_prediksi IS NULL";

$result = mysqli_query($koneksi, $sql);

// Proses prediksi jika ada request POST
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['prediksi'])) {
    $id_nasabah = $_POST['id_nasabah'];
    
    if (!$id_nasabah) {
        $error_message = "ID Nasabah tidak valid.";
    } else {
        // Ambil data nasabah
        $query_nasabah = "SELECT * FROM nasabah WHERE id_nasabah = ?";
        $stmt_nasabah = mysqli_prepare($koneksi, $query_nasabah);
        mysqli_stmt_bind_param($stmt_nasabah, "i", $id_nasabah);
        mysqli_stmt_execute($stmt_nasabah);
        $result_nasabah = mysqli_stmt_get_result($stmt_nasabah);
        $nasabah_data = mysqli_fetch_assoc($result_nasabah);
        mysqli_stmt_close($stmt_nasabah);

        if ($nasabah_data) {
            try {
                // URL API model Python
                $api_url = 'http://localhost:5000/api/predict';

                // Hitung nilai-nilai yang diperlukan
                $penghasilan = (float)$nasabah_data['penghasilan'];
                $jumlah_pinjaman = (float)$nasabah_data['jumlah_pinjaman'];
                $jumlah_tanggungan = isset($nasabah_data['jumlah_tanggungan']) ? max(1, (int)$nasabah_data['jumlah_tanggungan']) : 1;
                $jangka_waktu = isset($nasabah_data['jangka_waktu']) ? (int)$nasabah_data['jangka_waktu'] : 12;
                
                // Hitung umur kendaraan
                $umur_kendaraan = isset($nasabah_data['tahun_kendaraan']) ? (date('Y') - (int)$nasabah_data['tahun_kendaraan']) : 0;
                
                // Hitung penghasilan per tanggungan
                $penghasilan_per_tanggungan = $penghasilan / $jumlah_tanggungan;
                
                // Hitung rasio pinjaman terhadap penghasilan
                $rasio_pinjaman_penghasilan = $jumlah_pinjaman / ($penghasilan * 12);

                // Siapkan data untuk dikirim ke API
                $data_untuk_api = [
                    'umur' => (int)$nasabah_data['umur'],
                    'jenis_kelamin' => $nasabah_data['jenis_kelamin'],
                    'pekerjaan' => $nasabah_data['pekerjaan'],
                    'penghasilan' => (float)$penghasilan,
                    'jumlah_tanggungan' => (int)$jumlah_tanggungan,
                    'jumlah_pinjaman' => (float)$jumlah_pinjaman,
                    'jangka_waktu' => (int)$jangka_waktu,
                    'kepemilikan_rumah' => $nasabah_data['kepemilikan_rumah'],
                    'jaminan' => $nasabah_data['jaminan'],
                    'tahun_kendaraan' => (int)$nasabah_data['tahun_kendaraan'],
                    'status_pajak' => $nasabah_data['status_pajak'],
                    'tujuan_pinjaman' => !empty($nasabah_data['tujuan_pinjaman']) ? $nasabah_data['tujuan_pinjaman'] : 'Kebutuhan Pribadi',
                    'umur_kendaraan' => (int)$umur_kendaraan,
                    'rasio_pinjaman_penghasilan' => (float)$rasio_pinjaman_penghasilan,
                    'penghasilan_per_tanggungan' => (float)$penghasilan_per_tanggungan
                ];

                // Konversi ke JSON
                $json_data = json_encode($data_untuk_api);

                // Log untuk debugging
                error_log("Mengirim request ke API model Python: " . $json_data);

                // Inisialisasi cURL
                $ch = curl_init($api_url);

                // Set opsi cURL dengan timeout yang lebih panjang
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($json_data)
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30); // Timeout 30 detik
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // Connection timeout 10 detik
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

                // Eksekusi request
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $curl_error = curl_error($ch);

                // Tutup cURL
                curl_close($ch);

                // Log untuk debugging
                error_log("Response dari API model Python (HTTP $http_code): " . $response);

                if ($response === false || !empty($curl_error)) {
                    error_log("cURL Error: " . $curl_error);
                    // Fallback: gunakan prediksi sederhana jika API tidak tersedia
                    throw new Exception("API model Python tidak tersedia. Menggunakan prediksi fallback.");
                }

                if ($http_code !== 200) {
                    error_log("HTTP Error: " . $http_code . " Response: " . $response);
                    throw new Exception("API model Python mengembalikan status error: " . $http_code);
                }

                // Parse hasil dari API
                $hasil_api = json_decode($response, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new Exception("Response API tidak valid JSON: " . $response);
                }

                if (isset($hasil_api['error'])) {
                    throw new Exception("API model Python mengembalikan error: " . $hasil_api['error']);
                }

                // Simpan hasil prediksi ke database
                $hasil_prediksi = $hasil_api['hasil_prediksi'];
                $probabilitas = $hasil_api['probabilitas'];
                $keterangan = $hasil_api['keterangan'] ?? 'Prediksi menggunakan model backpropagation neural network';

                $query_hasil = "INSERT INTO hasil_prediksi (id_nasabah, hasil_prediksi, probabilitas, keterangan, tanggal_prediksi)
                                VALUES (?, ?, ?, ?, NOW())";
                $stmt_hasil = mysqli_prepare($koneksi, $query_hasil);
                mysqli_stmt_bind_param($stmt_hasil, "isds", $id_nasabah, $hasil_prediksi, $probabilitas, $keterangan);

                if (mysqli_stmt_execute($stmt_hasil)) {
                    $id_prediksi = mysqli_insert_id($koneksi);
                    $success_message = "Prediksi berhasil dilakukan untuk nasabah " . htmlspecialchars($nasabah_data['nama_nasabah']) . ".";

                    // Redirect ke halaman hasil prediksi
                    header("Location: hasil_prediksi.php?id_nasabah=$id_nasabah&id_prediksi=$id_prediksi");
                    exit;
                } else {
                    $error_message = "Gagal menyimpan hasil prediksi: " . mysqli_error($koneksi);
                }

                // Close statement untuk hasil prediksi
                if (isset($stmt_hasil)) {
                    mysqli_stmt_close($stmt_hasil);
                }
            } catch (Exception $e) {
                error_log("Error prediksi API: " . $e->getMessage());
                
                // Fallback: gunakan prediksi sederhana berdasarkan aturan bisnis
                try {
                    // Include fungsi prediksi sederhana dari prediksi_baru.php
                    require_once 'prediksi_baru.php';
                    $hasil_prediksi_fallback = prediksi_sederhana($nasabah_data);
                    
                    // Simpan hasil prediksi fallback
                    $query_hasil = "INSERT INTO hasil_prediksi (id_nasabah, hasil_prediksi, probabilitas, keterangan, tanggal_prediksi)
                                    VALUES (?, ?, ?, ?, NOW())";
                    $stmt_hasil = mysqli_prepare($koneksi, $query_hasil);
                    mysqli_stmt_bind_param($stmt_hasil, "isds", 
                        $id_nasabah, 
                        $hasil_prediksi_fallback['hasil_prediksi'], 
                        $hasil_prediksi_fallback['probabilitas'], 
                        $hasil_prediksi_fallback['keterangan']
                    );

                    if (mysqli_stmt_execute($stmt_hasil)) {
                        $id_prediksi = mysqli_insert_id($koneksi);
                        $success_message = "Prediksi berhasil dilakukan untuk nasabah " . htmlspecialchars($nasabah_data['nama_nasabah']) . " (menggunakan algoritma fallback).";

                        // Redirect ke halaman hasil prediksi
                        header("Location: hasil_prediksi.php?id_nasabah=$id_nasabah&id_prediksi=$id_prediksi");
                        exit;
                    } else {
                        $error_message = "Gagal menyimpan hasil prediksi fallback: " . mysqli_error($koneksi);
                    }

                    if (isset($stmt_hasil)) {
                        mysqli_stmt_close($stmt_hasil);
                    }
                } catch (Exception $fallback_error) {
                    $error_message = "Error saat melakukan prediksi: " . $e->getMessage() . " (Fallback juga gagal: " . $fallback_error->getMessage() . ")";
                    error_log("Error prediksi fallback: " . $fallback_error->getMessage());
                }
            }
        } else {
            $error_message = "Data nasabah tidak ditemukan.";
        }
    }
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Data Pengajuan dari Website</h1>
            </div>
        </div>

        <!-- Tampilkan pesan error jika ada -->
        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fa fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <!-- Tampilkan pesan sukses jika ada -->
        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fa fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <div class="panel panel-default">
            <div class="panel-heading">
                Daftar Pengajuan yang Belum Diproses
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-hover" id="dataTables-example">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Nama Nasabah</th>
                                <th>Penghasilan</th>
                                <th>Jumlah Pinjaman</th>
                                <th>Jangka Waktu</th>
                                <th>Tanggal Pengajuan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $no = 1;
                            while ($row = mysqli_fetch_assoc($result)): 
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo htmlspecialchars($row['nama_nasabah']); ?></td>
                                <td><?php echo formatRupiah($row['penghasilan']); ?></td>
                                <td><?php echo formatRupiah($row['jumlah_pinjaman']); ?></td>
                                <td><?php echo $row['jangka_waktu']; ?> bulan</td>
                                <td><?php echo date('d/m/Y', strtotime($row['tanggal_input'])); ?></td>
                                <td>
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="id_nasabah" value="<?php echo $row['id_nasabah']; ?>">
                                        <button type="submit" name="prediksi" class="btn btn-primary btn-sm" 
                                                onclick="return confirm('Yakin ingin melakukan prediksi untuk nasabah <?php echo htmlspecialchars($row['nama_nasabah']); ?>?')">
                                            <i class="fa fa-cogs"></i> Prediksi
                                        </button>
                                    </form>
                                    <a href="detail_nasabah.php?id=<?php echo $row['id_nasabah']; ?>" class="btn btn-info btn-sm">
                                        <i class="fa fa-eye"></i> Detail
                                    </a>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php 
require_once 'foot.php';
ob_end_flush();
?>

<script>
$(document).ready(function() {
    $('#dataTables-example').DataTable({
        responsive: true,
        "language": {
            "lengthMenu": "Tampilkan _MENU_ entri per halaman",
            "zeroRecords": "Tidak ada data yang ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total entri)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        },
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        "pageLength": 25,
        "order": [[ 0, "asc" ]]
    });
});
</script>

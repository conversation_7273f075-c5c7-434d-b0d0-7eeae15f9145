<?php
// Aktifkan output buffering
ob_start();

// Include file koneksi database dan helper
include 'koneksi.php';
include 'date_helper.php';

// Pastikan session sudah dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include file cek session untuk memastikan user sudah login
include 'cek_session.php';

// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);

// Include file navigasi untuk analis
require_once 'nav_analis.php';

$error_message = '';
$success_message = '';

// Query untuk mengambil data nasabah yang berasal dari website dan belum diprediksi
$sql = "SELECT n.* 
        FROM nasabah n 
        LEFT JOIN hasil_prediksi hp ON n.id_nasabah = hp.id_nasabah 
        WHERE n.sumber = 'website' 
        AND hp.id_prediksi IS NULL";

$result = mysqli_query($koneksi, $sql);

// Proses prediksi jika ada request POST
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['prediksi'])) {
    $id_nasabah = $_POST['id_nasabah'];
    
    if (!$id_nasabah) {
        $error_message = "ID Nasabah tidak valid.";
    } else {
        // Ambil data nasabah
        $query_nasabah = "SELECT * FROM nasabah WHERE id_nasabah = ?";
        $stmt_nasabah = mysqli_prepare($koneksi, $query_nasabah);
        mysqli_stmt_bind_param($stmt_nasabah, "i", $id_nasabah);
        mysqli_stmt_execute($stmt_nasabah);
        $result_nasabah = mysqli_stmt_get_result($stmt_nasabah);
        $nasabah_data = mysqli_fetch_assoc($result_nasabah);
        mysqli_stmt_close($stmt_nasabah);

        if ($nasabah_data) {
            try {
                // Gunakan api_predict.php untuk melakukan prediksi
                require_once 'api_predict.php';

                // Lakukan prediksi dengan model backpropagation
                $hasil_prediksi = prediksi_dengan_backpropagation($nasabah_data);

                // Simpan hasil prediksi ke database
                $id_prediksi = simpan_hasil_prediksi($nasabah_data, $hasil_prediksi);

                if ($id_prediksi) {
                    $success_message = "Prediksi berhasil dilakukan untuk nasabah " . htmlspecialchars($nasabah_data['nama_nasabah']) . ".";

                    // Redirect ke halaman hasil prediksi
                    header("Location: hasil_prediksi.php?id_nasabah=$id_nasabah&id_prediksi=$id_prediksi");
                    exit;
                } else {
                    $error_message = "Gagal menyimpan hasil prediksi.";
                }
            } catch (Exception $e) {
                $error_message = "Error saat melakukan prediksi: " . $e->getMessage();
                error_log("Error prediksi: " . $e->getMessage());
            }
        } else {
            $error_message = "Data nasabah tidak ditemukan.";
        }
    }
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Data Pengajuan dari Website</h1>
            </div>
        </div>

        <!-- Tampilkan pesan error jika ada -->
        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fa fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <!-- Tampilkan pesan sukses jika ada -->
        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fa fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <div class="panel panel-default">
            <div class="panel-heading">
                Daftar Pengajuan yang Belum Diproses
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-hover" id="dataTables-example">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Nama Nasabah</th>
                                <th>Penghasilan</th>
                                <th>Jumlah Pinjaman</th>
                                <th>Jangka Waktu</th>
                                <th>Tanggal Pengajuan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $no = 1;
                            while ($row = mysqli_fetch_assoc($result)): 
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo htmlspecialchars($row['nama_nasabah']); ?></td>
                                <td><?php echo formatRupiah($row['penghasilan']); ?></td>
                                <td><?php echo formatRupiah($row['jumlah_pinjaman']); ?></td>
                                <td><?php echo $row['jangka_waktu']; ?> bulan</td>
                                <td><?php echo date('d/m/Y', strtotime($row['tanggal_input'])); ?></td>
                                <td>
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="id_nasabah" value="<?php echo $row['id_nasabah']; ?>">
                                        <button type="submit" name="prediksi" class="btn btn-primary btn-sm" 
                                                onclick="return confirm('Yakin ingin melakukan prediksi untuk nasabah <?php echo htmlspecialchars($row['nama_nasabah']); ?>?')">
                                            <i class="fa fa-cogs"></i> Prediksi
                                        </button>
                                    </form>
                                    <a href="detail_nasabah.php?id=<?php echo $row['id_nasabah']; ?>" class="btn btn-info btn-sm">
                                        <i class="fa fa-eye"></i> Detail
                                    </a>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php 
require_once 'foot.php';
ob_end_flush();
?>

<script>
$(document).ready(function() {
    $('#dataTables-example').DataTable({
        responsive: true,
        "language": {
            "lengthMenu": "Tampilkan _MENU_ entri per halaman",
            "zeroRecords": "Tidak ada data yang ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total entri)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        },
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        "pageLength": 25,
        "order": [[ 0, "asc" ]]
    });
});
</script>
